{"name": "synecx-self-checkout", "version": "0.1.0", "private": true, "scripts": {"export": "next build && next export", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^6.4.0", "@mui/material": "^6.4.0", "@prisma/client": "^6.2.1", "cors": "^2.8.5", "express": "^5.1.0", "mdb-react-ui-kit": "^9.0.0", "multer": "^2.0.2", "next": "15.1.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-toastify": "^11.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.5", "postcss": "^8", "prisma": "^6.2.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}