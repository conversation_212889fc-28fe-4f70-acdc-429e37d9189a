{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/components/cart/header.tsx"], "sourcesContent": ["import Image from 'next/image'\r\nimport React from 'react'\r\n\r\nconst Header = () => {\r\n  return (\r\n    <div className='w-full h-20 sticky top-0 flex justify-center items-center'><Image src={'/Logo.svg'} alt={''} width={200} height={100} /></div>\r\n  )\r\n}\r\n\r\nexport default Header"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAI,WAAU;kBAA4D,cAAA,8OAAC,6HAAA,CAAA,UAAK;YAAC,KAAK;YAAa,KAAK;YAAI,OAAO;YAAK,QAAQ;;;;;;;;;;;AAErI;uCAEe"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/app/cart/page.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect } from \"react\";\nimport Cart from \"@/components/cart/cart\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport { Button, Box, Typography } from \"@mui/material\";\n\nconst CartPage: React.FC = () => {\n  const [totalPrice, setTotalPrice] = useState<number>(0);\n\n  const handleTotalPriceUpdate = (price: number) => {\n    setTotalPrice(price);\n  };\n\n  // Add some demo items for testing\n  const addDemoItems = () => {\n    const demoItems = [\n      {\n        productName: \"Milk Assorted\",\n        weight_flag: 1,\n        alternatives: [\"Milk Assorted\", \"Dairy Milk\", \"Fresh Milk\"],\n        weight: 1.5,\n        price: 60,\n        fromPredict: false\n      },\n      {\n        productName: \"POTATO-CHIPS\",\n        weight_flag: 0,\n        alternatives: [\"POTATO-CHIPS\", \"Lays Chips\", \"Bingo Chips\"],\n        quantity: 2,\n        price: 20,\n        fromPredict: false\n      },\n      {\n        productName: \"KAJU-KATLI\",\n        weight_flag: 1,\n        alternatives: [\"KAJU-KATLI\", \"Cashew Sweets\", \"Haldiram Kaju\"],\n        weight: 0.5,\n        price: 800,\n        fromPredict: false\n      }\n    ];\n\n    localStorage.setItem(\"cartItems\", JSON.stringify(demoItems));\n    window.location.reload(); // Reload to show the items\n  };\n\n  const clearCart = () => {\n    localStorage.removeItem(\"cartItems\");\n    window.location.reload();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\">\n      {/* Demo Controls */}\n      <Box sx={{\n        position: 'fixed',\n        top: 20,\n        right: 20,\n        zIndex: 1000,\n        display: 'flex',\n        gap: 2,\n        flexDirection: 'column',\n        alignItems: 'flex-end'\n      }}>\n        <Typography variant=\"body2\" sx={{\n          bgcolor: 'rgba(0,0,0,0.7)',\n          color: 'white',\n          px: 2,\n          py: 1,\n          borderRadius: 2\n        }}>\n          Demo Controls\n        </Typography>\n        <Button\n          onClick={addDemoItems}\n          variant=\"contained\"\n          sx={{\n            bgcolor: '#16a34a',\n            '&:hover': { bgcolor: '#15803d' },\n            borderRadius: '12px'\n          }}\n        >\n          Add Demo Items\n        </Button>\n        <Button\n          onClick={clearCart}\n          variant=\"outlined\"\n          sx={{\n            borderColor: '#dc2626',\n            color: '#dc2626',\n            '&:hover': {\n              bgcolor: '#fee2e2',\n              borderColor: '#dc2626'\n            },\n            borderRadius: '12px'\n          }}\n        >\n          Clear All\n        </Button>\n      </Box>\n\n      <Cart onUpdateTotalPrice={handleTotalPriceUpdate} />\n      <ToastContainer\n        position=\"top-right\"\n        autoClose={3000}\n        hideProgressBar={false}\n        newestOnTop={false}\n        closeOnClick\n        rtl={false}\n        pauseOnFocusLoss\n        draggable\n        pauseOnHover\n        theme=\"light\"\n      />\n    </div>\n  );\n};\n\nexport default CartPage;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;AAAA;AAAA;AALA;;;;;;;AAOA,MAAM,WAAqB;IACzB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,MAAM,yBAAyB,CAAC;QAC9B,cAAc;IAChB;IAEA,kCAAkC;IAClC,MAAM,eAAe;QACnB,MAAM,YAAY;YAChB;gBACE,aAAa;gBACb,aAAa;gBACb,cAAc;oBAAC;oBAAiB;oBAAc;iBAAa;gBAC3D,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,aAAa;gBACb,aAAa;gBACb,cAAc;oBAAC;oBAAgB;oBAAc;iBAAc;gBAC3D,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,aAAa;gBACb,aAAa;gBACb,cAAc;oBAAC;oBAAc;oBAAiB;iBAAgB;gBAC9D,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;SACD;QAED,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACjD,OAAO,QAAQ,CAAC,MAAM,IAAI,2BAA2B;IACvD;IAEA,MAAM,YAAY;QAChB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,iLAAA,CAAA,MAAG;gBAAC,IAAI;oBACP,UAAU;oBACV,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,SAAS;oBACT,KAAK;oBACL,eAAe;oBACf,YAAY;gBACd;;kCACE,8OAAC,sMAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAQ,IAAI;4BAC9B,SAAS;4BACT,OAAO;4BACP,IAAI;4BACJ,IAAI;4BACJ,cAAc;wBAChB;kCAAG;;;;;;kCAGH,8OAAC,0LAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;wBACR,IAAI;4BACF,SAAS;4BACT,WAAW;gCAAE,SAAS;4BAAU;4BAChC,cAAc;wBAChB;kCACD;;;;;;kCAGD,8OAAC,0LAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;wBACR,IAAI;4BACF,aAAa;4BACb,OAAO;4BACP,WAAW;gCACT,SAAS;gCACT,aAAa;4BACf;4BACA,cAAc;wBAChB;kCACD;;;;;;;;;;;;0BAKH,8OAAC,kIAAA,CAAA,UAAI;gBAAC,oBAAoB;;;;;;0BAC1B,8OAAC,mJAAA,CAAA,iBAAc;gBACb,UAAS;gBACT,WAAW;gBACX,iBAAiB;gBACjB,aAAa;gBACb,YAAY;gBACZ,KAAK;gBACL,gBAAgB;gBAChB,SAAS;gBACT,YAAY;gBACZ,OAAM;;;;;;;;;;;;AAId;uCAEe"}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}