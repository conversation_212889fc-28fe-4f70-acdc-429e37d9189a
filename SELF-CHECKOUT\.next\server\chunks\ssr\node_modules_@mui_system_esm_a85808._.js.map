{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/createBreakpoints/createBreakpoints.js"], "sourcesContent": ["// Sorted ASC by size. That's important.\n// It can't be configured as it's used statically for propTypes.\nexport const breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\nconst sortBreakpointsValues = values => {\n  const breakpointsAsArray = Object.keys(values).map(key => ({\n    key,\n    val: values[key]\n  })) || [];\n  // Sort in ascending order\n  breakpointsAsArray.sort((breakpoint1, breakpoint2) => breakpoint1.val - breakpoint2.val);\n  return breakpointsAsArray.reduce((acc, obj) => {\n    return {\n      ...acc,\n      [obj.key]: obj.val\n    };\n  }, {});\n};\n\n// Keep in mind that @media is inclusive by the CSS specification.\nexport default function createBreakpoints(breakpoints) {\n  const {\n    // The breakpoint **start** at this value.\n    // For instance with the first breakpoint xs: [xs, sm).\n    values = {\n      xs: 0,\n      // phone\n      sm: 600,\n      // tablet\n      md: 900,\n      // small laptop\n      lg: 1200,\n      // desktop\n      xl: 1536 // large screen\n    },\n    unit = 'px',\n    step = 5,\n    ...other\n  } = breakpoints;\n  const sortedValues = sortBreakpointsValues(values);\n  const keys = Object.keys(sortedValues);\n  function up(key) {\n    const value = typeof values[key] === 'number' ? values[key] : key;\n    return `@media (min-width:${value}${unit})`;\n  }\n  function down(key) {\n    const value = typeof values[key] === 'number' ? values[key] : key;\n    return `@media (max-width:${value - step / 100}${unit})`;\n  }\n  function between(start, end) {\n    const endIndex = keys.indexOf(end);\n    return `@media (min-width:${typeof values[start] === 'number' ? values[start] : start}${unit}) and ` + `(max-width:${(endIndex !== -1 && typeof values[keys[endIndex]] === 'number' ? values[keys[endIndex]] : end) - step / 100}${unit})`;\n  }\n  function only(key) {\n    if (keys.indexOf(key) + 1 < keys.length) {\n      return between(key, keys[keys.indexOf(key) + 1]);\n    }\n    return up(key);\n  }\n  function not(key) {\n    // handle first and last key separately, for better readability\n    const keyIndex = keys.indexOf(key);\n    if (keyIndex === 0) {\n      return up(keys[1]);\n    }\n    if (keyIndex === keys.length - 1) {\n      return down(keys[keyIndex]);\n    }\n    return between(key, keys[keys.indexOf(key) + 1]).replace('@media', '@media not all and');\n  }\n  return {\n    keys,\n    values: sortedValues,\n    up,\n    down,\n    between,\n    only,\n    not,\n    unit,\n    ...other\n  };\n}"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,gEAAgE;;;;;AACzD,MAAM,iBAAiB;IAAC;IAAM;IAAM;IAAM;IAAM;CAAK;AAC5D,MAAM,wBAAwB,CAAA;IAC5B,MAAM,qBAAqB,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA,MAAO,CAAC;YACzD;YACA,KAAK,MAAM,CAAC,IAAI;QAClB,CAAC,MAAM,EAAE;IACT,0BAA0B;IAC1B,mBAAmB,IAAI,CAAC,CAAC,aAAa,cAAgB,YAAY,GAAG,GAAG,YAAY,GAAG;IACvF,OAAO,mBAAmB,MAAM,CAAC,CAAC,KAAK;QACrC,OAAO;YACL,GAAG,GAAG;YACN,CAAC,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG;QACpB;IACF,GAAG,CAAC;AACN;AAGe,SAAS,kBAAkB,WAAW;IACnD,MAAM,EACJ,0CAA0C;IAC1C,uDAAuD;IACvD,SAAS;QACP,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,eAAe;QACf,IAAI;QACJ,UAAU;QACV,IAAI,KAAK,eAAe;IAC1B,CAAC,EACD,OAAO,IAAI,EACX,OAAO,CAAC,EACR,GAAG,OACJ,GAAG;IACJ,MAAM,eAAe,sBAAsB;IAC3C,MAAM,OAAO,OAAO,IAAI,CAAC;IACzB,SAAS,GAAG,GAAG;QACb,MAAM,QAAQ,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,MAAM,CAAC,IAAI,GAAG;QAC9D,OAAO,CAAC,kBAAkB,EAAE,QAAQ,KAAK,CAAC,CAAC;IAC7C;IACA,SAAS,KAAK,GAAG;QACf,MAAM,QAAQ,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,MAAM,CAAC,IAAI,GAAG;QAC9D,OAAO,CAAC,kBAAkB,EAAE,QAAQ,OAAO,MAAM,KAAK,CAAC,CAAC;IAC1D;IACA,SAAS,QAAQ,KAAK,EAAE,GAAG;QACzB,MAAM,WAAW,KAAK,OAAO,CAAC;QAC9B,OAAO,CAAC,kBAAkB,EAAE,OAAO,MAAM,CAAC,MAAM,KAAK,WAAW,MAAM,CAAC,MAAM,GAAG,QAAQ,KAAK,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,aAAa,CAAC,KAAK,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,OAAO,MAAM,KAAK,CAAC,CAAC;IAC5O;IACA,SAAS,KAAK,GAAG;QACf,IAAI,KAAK,OAAO,CAAC,OAAO,IAAI,KAAK,MAAM,EAAE;YACvC,OAAO,QAAQ,KAAK,IAAI,CAAC,KAAK,OAAO,CAAC,OAAO,EAAE;QACjD;QACA,OAAO,GAAG;IACZ;IACA,SAAS,IAAI,GAAG;QACd,+DAA+D;QAC/D,MAAM,WAAW,KAAK,OAAO,CAAC;QAC9B,IAAI,aAAa,GAAG;YAClB,OAAO,GAAG,IAAI,CAAC,EAAE;QACnB;QACA,IAAI,aAAa,KAAK,MAAM,GAAG,GAAG;YAChC,OAAO,KAAK,IAAI,CAAC,SAAS;QAC5B;QACA,OAAO,QAAQ,KAAK,IAAI,CAAC,KAAK,OAAO,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,UAAU;IACrE;IACA,OAAO;QACL;QACA,QAAQ;QACR;QACA;QACA;QACA;QACA;QACA;QACA,GAAG,KAAK;IACV;AACF", "ignoreList": [0]}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/responsivePropType/responsivePropType.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nconst responsivePropType = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.object, PropTypes.array]) : {};\nexport default responsivePropType;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,qBAAqB,uCAAwC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;IAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;IAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;IAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;IAAE,sIAAA,CAAA,UAAS,CAAC,KAAK;CAAC;uCAC/I", "ignoreList": [0]}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/memoize/memoize.js"], "sourcesContent": ["export default function memoize(fn) {\n  const cache = {};\n  return arg => {\n    if (cache[arg] === undefined) {\n      cache[arg] = fn(arg);\n    }\n    return cache[arg];\n  };\n}"], "names": [], "mappings": ";;;AAAe,SAAS,QAAQ,EAAE;IAChC,MAAM,QAAQ,CAAC;IACf,OAAO,CAAA;QACL,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW;YAC5B,KAAK,CAAC,IAAI,GAAG,GAAG;QAClB;QACA,OAAO,KAAK,CAAC,IAAI;IACnB;AACF", "ignoreList": [0]}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/cssContainerQueries/cssContainerQueries.js"], "sourcesContent": ["import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n/**\n * For using in `sx` prop to sort the breakpoint from low to high.\n * Note: this function does not work and will not support multiple units.\n *       e.g. input: { '@container (min-width:300px)': '1rem', '@container (min-width:40rem)': '2rem' }\n *            output: { '@container (min-width:40rem)': '2rem', '@container (min-width:300px)': '1rem' } // since 40 < 300 eventhough 40rem > 300px\n */\nexport function sortContainerQueries(theme, css) {\n  if (!theme.containerQueries) {\n    return css;\n  }\n  const sorted = Object.keys(css).filter(key => key.startsWith('@container')).sort((a, b) => {\n    const regex = /min-width:\\s*([0-9.]+)/;\n    return +(a.match(regex)?.[1] || 0) - +(b.match(regex)?.[1] || 0);\n  });\n  if (!sorted.length) {\n    return css;\n  }\n  return sorted.reduce((acc, key) => {\n    const value = css[key];\n    delete acc[key];\n    acc[key] = value;\n    return acc;\n  }, {\n    ...css\n  });\n}\nexport function isCqShorthand(breakpointKeys, value) {\n  return value === '@' || value.startsWith('@') && (breakpointKeys.some(key => value.startsWith(`@${key}`)) || !!value.match(/^@\\d/));\n}\nexport function getContainerQuery(theme, shorthand) {\n  const matches = shorthand.match(/^@([^/]+)?\\/?(.+)?$/);\n  if (!matches) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The provided shorthand ${`(${shorthand})`} is invalid. The format should be \\`@<breakpoint | number>\\` or \\`@<breakpoint | number>/<container>\\`.\\n` + 'For example, `@sm` or `@600` or `@40rem/sidebar`.' : _formatMuiErrorMessage(18, `(${shorthand})`));\n    }\n    return null;\n  }\n  const [, containerQuery, containerName] = matches;\n  const value = Number.isNaN(+containerQuery) ? containerQuery || 0 : +containerQuery;\n  return theme.containerQueries(containerName).up(value);\n}\nexport default function cssContainerQueries(themeInput) {\n  const toContainerQuery = (mediaQuery, name) => mediaQuery.replace('@media', name ? `@container ${name}` : '@container');\n  function attachCq(node, name) {\n    node.up = (...args) => toContainerQuery(themeInput.breakpoints.up(...args), name);\n    node.down = (...args) => toContainerQuery(themeInput.breakpoints.down(...args), name);\n    node.between = (...args) => toContainerQuery(themeInput.breakpoints.between(...args), name);\n    node.only = (...args) => toContainerQuery(themeInput.breakpoints.only(...args), name);\n    node.not = (...args) => {\n      const result = toContainerQuery(themeInput.breakpoints.not(...args), name);\n      if (result.includes('not all and')) {\n        // `@container` does not work with `not all and`, so need to invert the logic\n        return result.replace('not all and ', '').replace('min-width:', 'width<').replace('max-width:', 'width>').replace('and', 'or');\n      }\n      return result;\n    };\n  }\n  const node = {};\n  const containerQueries = name => {\n    attachCq(node, name);\n    return node;\n  };\n  attachCq(containerQueries);\n  return {\n    ...themeInput,\n    containerQueries\n  };\n}"], "names": [], "mappings": ";;;;;;;AAOO,SAAS,qBAAqB,KAAK,EAAE,GAAG;IAC7C,IAAI,CAAC,MAAM,gBAAgB,EAAE;QAC3B,OAAO;IACT;IACA,MAAM,SAAS,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG;QACnF,MAAM,QAAQ;QACd,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;IACjE;IACA,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,OAAO;IACT;IACA,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK;QACzB,MAAM,QAAQ,GAAG,CAAC,IAAI;QACtB,OAAO,GAAG,CAAC,IAAI;QACf,GAAG,CAAC,IAAI,GAAG;QACX,OAAO;IACT,GAAG;QACD,GAAG,GAAG;IACR;AACF;AACO,SAAS,cAAc,cAAc,EAAE,KAAK;IACjD,OAAO,UAAU,OAAO,MAAM,UAAU,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,CAAA,MAAO,MAAM,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,OAAO;AACpI;AACO,SAAS,kBAAkB,KAAK,EAAE,SAAS;IAChD,MAAM,UAAU,UAAU,KAAK,CAAC;IAChC,IAAI,CAAC,SAAS;QACZ,wCAA2C;YACzC,MAAM,IAAI,MAAM,uCAAwC,CAAC,4BAA4B,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,yGAAyG,CAAC,GAAG;QACvN;QACA,OAAO;IACT;IACA,MAAM,GAAG,gBAAgB,cAAc,GAAG;IAC1C,MAAM,QAAQ,OAAO,KAAK,CAAC,CAAC,kBAAkB,kBAAkB,IAAI,CAAC;IACrE,OAAO,MAAM,gBAAgB,CAAC,eAAe,EAAE,CAAC;AAClD;AACe,SAAS,oBAAoB,UAAU;IACpD,MAAM,mBAAmB,CAAC,YAAY,OAAS,WAAW,OAAO,CAAC,UAAU,OAAO,CAAC,WAAW,EAAE,MAAM,GAAG;IAC1G,SAAS,SAAS,IAAI,EAAE,IAAI;QAC1B,KAAK,EAAE,GAAG,CAAC,GAAG,OAAS,iBAAiB,WAAW,WAAW,CAAC,EAAE,IAAI,OAAO;QAC5E,KAAK,IAAI,GAAG,CAAC,GAAG,OAAS,iBAAiB,WAAW,WAAW,CAAC,IAAI,IAAI,OAAO;QAChF,KAAK,OAAO,GAAG,CAAC,GAAG,OAAS,iBAAiB,WAAW,WAAW,CAAC,OAAO,IAAI,OAAO;QACtF,KAAK,IAAI,GAAG,CAAC,GAAG,OAAS,iBAAiB,WAAW,WAAW,CAAC,IAAI,IAAI,OAAO;QAChF,KAAK,GAAG,GAAG,CAAC,GAAG;YACb,MAAM,SAAS,iBAAiB,WAAW,WAAW,CAAC,GAAG,IAAI,OAAO;YACrE,IAAI,OAAO,QAAQ,CAAC,gBAAgB;gBAClC,6EAA6E;gBAC7E,OAAO,OAAO,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,cAAc,UAAU,OAAO,CAAC,cAAc,UAAU,OAAO,CAAC,OAAO;YAC3H;YACA,OAAO;QACT;IACF;IACA,MAAM,OAAO,CAAC;IACd,MAAM,mBAAmB,CAAA;QACvB,SAAS,MAAM;QACf,OAAO;IACT;IACA,SAAS;IACT,OAAO;QACL,GAAG,UAAU;QACb;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/merge/merge.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nfunction merge(acc, item) {\n  if (!item) {\n    return acc;\n  }\n  return deepmerge(acc, item, {\n    clone: false // No need to clone deep, it's way faster.\n  });\n}\nexport default merge;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,MAAM,GAAG,EAAE,IAAI;IACtB,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,KAAK,MAAM;QAC1B,OAAO,MAAM,0CAA0C;IACzD;AACF;uCACe", "ignoreList": [0]}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/breakpoints/breakpoints.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport deepmerge from '@mui/utils/deepmerge';\nimport merge from \"../merge/index.js\";\nimport { isCqShorthand, getContainerQuery } from \"../cssContainerQueries/index.js\";\n\n// The breakpoint **start** at this value.\n// For instance with the first breakpoint xs: [xs, sm[.\nexport const values = {\n  xs: 0,\n  // phone\n  sm: 600,\n  // tablet\n  md: 900,\n  // small laptop\n  lg: 1200,\n  // desktop\n  xl: 1536 // large screen\n};\nconst defaultBreakpoints = {\n  // Sorted ASC by size. That's important.\n  // It can't be configured as it's used statically for propTypes.\n  keys: ['xs', 'sm', 'md', 'lg', 'xl'],\n  up: key => `@media (min-width:${values[key]}px)`\n};\nconst defaultContainerQueries = {\n  containerQueries: containerName => ({\n    up: key => {\n      let result = typeof key === 'number' ? key : values[key] || key;\n      if (typeof result === 'number') {\n        result = `${result}px`;\n      }\n      return containerName ? `@container ${containerName} (min-width:${result})` : `@container (min-width:${result})`;\n    }\n  })\n};\nexport function handleBreakpoints(props, propValue, styleFromPropValue) {\n  const theme = props.theme || {};\n  if (Array.isArray(propValue)) {\n    const themeBreakpoints = theme.breakpoints || defaultBreakpoints;\n    return propValue.reduce((acc, item, index) => {\n      acc[themeBreakpoints.up(themeBreakpoints.keys[index])] = styleFromPropValue(propValue[index]);\n      return acc;\n    }, {});\n  }\n  if (typeof propValue === 'object') {\n    const themeBreakpoints = theme.breakpoints || defaultBreakpoints;\n    return Object.keys(propValue).reduce((acc, breakpoint) => {\n      if (isCqShorthand(themeBreakpoints.keys, breakpoint)) {\n        const containerKey = getContainerQuery(theme.containerQueries ? theme : defaultContainerQueries, breakpoint);\n        if (containerKey) {\n          acc[containerKey] = styleFromPropValue(propValue[breakpoint], breakpoint);\n        }\n      }\n      // key is breakpoint\n      else if (Object.keys(themeBreakpoints.values || values).includes(breakpoint)) {\n        const mediaKey = themeBreakpoints.up(breakpoint);\n        acc[mediaKey] = styleFromPropValue(propValue[breakpoint], breakpoint);\n      } else {\n        const cssKey = breakpoint;\n        acc[cssKey] = propValue[cssKey];\n      }\n      return acc;\n    }, {});\n  }\n  const output = styleFromPropValue(propValue);\n  return output;\n}\nfunction breakpoints(styleFunction) {\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const newStyleFunction = props => {\n    const theme = props.theme || {};\n    const base = styleFunction(props);\n    const themeBreakpoints = theme.breakpoints || defaultBreakpoints;\n    const extended = themeBreakpoints.keys.reduce((acc, key) => {\n      if (props[key]) {\n        acc = acc || {};\n        acc[themeBreakpoints.up(key)] = styleFunction({\n          theme,\n          ...props[key]\n        });\n      }\n      return acc;\n    }, null);\n    return merge(base, extended);\n  };\n  newStyleFunction.propTypes = process.env.NODE_ENV !== 'production' ? {\n    ...styleFunction.propTypes,\n    xs: PropTypes.object,\n    sm: PropTypes.object,\n    md: PropTypes.object,\n    lg: PropTypes.object,\n    xl: PropTypes.object\n  } : {};\n  newStyleFunction.filterProps = ['xs', 'sm', 'md', 'lg', 'xl', ...styleFunction.filterProps];\n  return newStyleFunction;\n}\nexport function createEmptyBreakpointObject(breakpointsInput = {}) {\n  const breakpointsInOrder = breakpointsInput.keys?.reduce((acc, key) => {\n    const breakpointStyleKey = breakpointsInput.up(key);\n    acc[breakpointStyleKey] = {};\n    return acc;\n  }, {});\n  return breakpointsInOrder || {};\n}\nexport function removeUnusedBreakpoints(breakpointKeys, style) {\n  return breakpointKeys.reduce((acc, key) => {\n    const breakpointOutput = acc[key];\n    const isBreakpointUnused = !breakpointOutput || Object.keys(breakpointOutput).length === 0;\n    if (isBreakpointUnused) {\n      delete acc[key];\n    }\n    return acc;\n  }, style);\n}\nexport function mergeBreakpointsInOrder(breakpointsInput, ...styles) {\n  const emptyBreakpoints = createEmptyBreakpointObject(breakpointsInput);\n  const mergedOutput = [emptyBreakpoints, ...styles].reduce((prev, next) => deepmerge(prev, next), {});\n  return removeUnusedBreakpoints(Object.keys(emptyBreakpoints), mergedOutput);\n}\n\n// compute base for responsive values; e.g.,\n// [1,2,3] => {xs: true, sm: true, md: true}\n// {xs: 1, sm: 2, md: 3} => {xs: true, sm: true, md: true}\nexport function computeBreakpointsBase(breakpointValues, themeBreakpoints) {\n  // fixed value\n  if (typeof breakpointValues !== 'object') {\n    return {};\n  }\n  const base = {};\n  const breakpointsKeys = Object.keys(themeBreakpoints);\n  if (Array.isArray(breakpointValues)) {\n    breakpointsKeys.forEach((breakpoint, i) => {\n      if (i < breakpointValues.length) {\n        base[breakpoint] = true;\n      }\n    });\n  } else {\n    breakpointsKeys.forEach(breakpoint => {\n      if (breakpointValues[breakpoint] != null) {\n        base[breakpoint] = true;\n      }\n    });\n  }\n  return base;\n}\nexport function resolveBreakpointValues({\n  values: breakpointValues,\n  breakpoints: themeBreakpoints,\n  base: customBase\n}) {\n  const base = customBase || computeBreakpointsBase(breakpointValues, themeBreakpoints);\n  const keys = Object.keys(base);\n  if (keys.length === 0) {\n    return breakpointValues;\n  }\n  let previous;\n  return keys.reduce((acc, breakpoint, i) => {\n    if (Array.isArray(breakpointValues)) {\n      acc[breakpoint] = breakpointValues[i] != null ? breakpointValues[i] : breakpointValues[previous];\n      previous = i;\n    } else if (typeof breakpointValues === 'object') {\n      acc[breakpoint] = breakpointValues[breakpoint] != null ? breakpointValues[breakpoint] : breakpointValues[previous];\n      previous = breakpoint;\n    } else {\n      acc[breakpoint] = breakpointValues;\n    }\n    return acc;\n  }, {});\n}\nexport default breakpoints;"], "names": [], "mappings": ";;;;;;;;;;AAGA;AADA;AAFA;AACA;;;;;AAMO,MAAM,SAAS;IACpB,IAAI;IACJ,QAAQ;IACR,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,eAAe;IACf,IAAI;IACJ,UAAU;IACV,IAAI,KAAK,eAAe;AAC1B;AACA,MAAM,qBAAqB;IACzB,wCAAwC;IACxC,gEAAgE;IAChE,MAAM;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IACpC,IAAI,CAAA,MAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;AAClD;AACA,MAAM,0BAA0B;IAC9B,kBAAkB,CAAA,gBAAiB,CAAC;YAClC,IAAI,CAAA;gBACF,IAAI,SAAS,OAAO,QAAQ,WAAW,MAAM,MAAM,CAAC,IAAI,IAAI;gBAC5D,IAAI,OAAO,WAAW,UAAU;oBAC9B,SAAS,GAAG,OAAO,EAAE,CAAC;gBACxB;gBACA,OAAO,gBAAgB,CAAC,WAAW,EAAE,cAAc,YAAY,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;YACjH;QACF,CAAC;AACH;AACO,SAAS,kBAAkB,KAAK,EAAE,SAAS,EAAE,kBAAkB;IACpE,MAAM,QAAQ,MAAM,KAAK,IAAI,CAAC;IAC9B,IAAI,MAAM,OAAO,CAAC,YAAY;QAC5B,MAAM,mBAAmB,MAAM,WAAW,IAAI;QAC9C,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK,MAAM;YAClC,GAAG,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,IAAI,CAAC,MAAM,EAAE,GAAG,mBAAmB,SAAS,CAAC,MAAM;YAC5F,OAAO;QACT,GAAG,CAAC;IACN;IACA,IAAI,OAAO,cAAc,UAAU;QACjC,MAAM,mBAAmB,MAAM,WAAW,IAAI;QAC9C,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK;YACzC,IAAI,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,IAAI,EAAE,aAAa;gBACpD,MAAM,eAAe,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,gBAAgB,GAAG,QAAQ,yBAAyB;gBACjG,IAAI,cAAc;oBAChB,GAAG,CAAC,aAAa,GAAG,mBAAmB,SAAS,CAAC,WAAW,EAAE;gBAChE;YACF,OAEK,IAAI,OAAO,IAAI,CAAC,iBAAiB,MAAM,IAAI,QAAQ,QAAQ,CAAC,aAAa;gBAC5E,MAAM,WAAW,iBAAiB,EAAE,CAAC;gBACrC,GAAG,CAAC,SAAS,GAAG,mBAAmB,SAAS,CAAC,WAAW,EAAE;YAC5D,OAAO;gBACL,MAAM,SAAS;gBACf,GAAG,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO;YACjC;YACA,OAAO;QACT,GAAG,CAAC;IACN;IACA,MAAM,SAAS,mBAAmB;IAClC,OAAO;AACT;AACA,SAAS,YAAY,aAAa;IAChC,iBAAiB;IACjB,+DAA+D;IAC/D,MAAM,mBAAmB,CAAA;QACvB,MAAM,QAAQ,MAAM,KAAK,IAAI,CAAC;QAC9B,MAAM,OAAO,cAAc;QAC3B,MAAM,mBAAmB,MAAM,WAAW,IAAI;QAC9C,MAAM,WAAW,iBAAiB,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK;YAClD,IAAI,KAAK,CAAC,IAAI,EAAE;gBACd,MAAM,OAAO,CAAC;gBACd,GAAG,CAAC,iBAAiB,EAAE,CAAC,KAAK,GAAG,cAAc;oBAC5C;oBACA,GAAG,KAAK,CAAC,IAAI;gBACf;YACF;YACA,OAAO;QACT,GAAG;QACH,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE,MAAM;IACrB;IACA,iBAAiB,SAAS,GAAG,uCAAwC;QACnE,GAAG,cAAc,SAAS;QAC1B,IAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;QACpB,IAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;QACpB,IAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;QACpB,IAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;QACpB,IAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;IACtB;IACA,iBAAiB,WAAW,GAAG;QAAC;QAAM;QAAM;QAAM;QAAM;WAAS,cAAc,WAAW;KAAC;IAC3F,OAAO;AACT;AACO,SAAS,4BAA4B,mBAAmB,CAAC,CAAC;IAC/D,MAAM,qBAAqB,iBAAiB,IAAI,EAAE,OAAO,CAAC,KAAK;QAC7D,MAAM,qBAAqB,iBAAiB,EAAE,CAAC;QAC/C,GAAG,CAAC,mBAAmB,GAAG,CAAC;QAC3B,OAAO;IACT,GAAG,CAAC;IACJ,OAAO,sBAAsB,CAAC;AAChC;AACO,SAAS,wBAAwB,cAAc,EAAE,KAAK;IAC3D,OAAO,eAAe,MAAM,CAAC,CAAC,KAAK;QACjC,MAAM,mBAAmB,GAAG,CAAC,IAAI;QACjC,MAAM,qBAAqB,CAAC,oBAAoB,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK;QACzF,IAAI,oBAAoB;YACtB,OAAO,GAAG,CAAC,IAAI;QACjB;QACA,OAAO;IACT,GAAG;AACL;AACO,SAAS,wBAAwB,gBAAgB,EAAE,GAAG,MAAM;IACjE,MAAM,mBAAmB,4BAA4B;IACrD,MAAM,eAAe;QAAC;WAAqB;KAAO,CAAC,MAAM,CAAC,CAAC,MAAM,OAAS,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,MAAM,OAAO,CAAC;IAClG,OAAO,wBAAwB,OAAO,IAAI,CAAC,mBAAmB;AAChE;AAKO,SAAS,uBAAuB,gBAAgB,EAAE,gBAAgB;IACvE,cAAc;IACd,IAAI,OAAO,qBAAqB,UAAU;QACxC,OAAO,CAAC;IACV;IACA,MAAM,OAAO,CAAC;IACd,MAAM,kBAAkB,OAAO,IAAI,CAAC;IACpC,IAAI,MAAM,OAAO,CAAC,mBAAmB;QACnC,gBAAgB,OAAO,CAAC,CAAC,YAAY;YACnC,IAAI,IAAI,iBAAiB,MAAM,EAAE;gBAC/B,IAAI,CAAC,WAAW,GAAG;YACrB;QACF;IACF,OAAO;QACL,gBAAgB,OAAO,CAAC,CAAA;YACtB,IAAI,gBAAgB,CAAC,WAAW,IAAI,MAAM;gBACxC,IAAI,CAAC,WAAW,GAAG;YACrB;QACF;IACF;IACA,OAAO;AACT;AACO,SAAS,wBAAwB,EACtC,QAAQ,gBAAgB,EACxB,aAAa,gBAAgB,EAC7B,MAAM,UAAU,EACjB;IACC,MAAM,OAAO,cAAc,uBAAuB,kBAAkB;IACpE,MAAM,OAAO,OAAO,IAAI,CAAC;IACzB,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IACA,IAAI;IACJ,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK,YAAY;QACnC,IAAI,MAAM,OAAO,CAAC,mBAAmB;YACnC,GAAG,CAAC,WAAW,GAAG,gBAAgB,CAAC,EAAE,IAAI,OAAO,gBAAgB,CAAC,EAAE,GAAG,gBAAgB,CAAC,SAAS;YAChG,WAAW;QACb,OAAO,IAAI,OAAO,qBAAqB,UAAU;YAC/C,GAAG,CAAC,WAAW,GAAG,gBAAgB,CAAC,WAAW,IAAI,OAAO,gBAAgB,CAAC,WAAW,GAAG,gBAAgB,CAAC,SAAS;YAClH,WAAW;QACb,OAAO;YACL,GAAG,CAAC,WAAW,GAAG;QACpB;QACA,OAAO;IACT,GAAG,CAAC;AACN;uCACe", "ignoreList": [0]}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/style/style.js"], "sourcesContent": ["import capitalize from '@mui/utils/capitalize';\nimport responsivePropType from \"../responsivePropType/index.js\";\nimport { handleBreakpoints } from \"../breakpoints/index.js\";\nexport function getPath(obj, path, checkVars = true) {\n  if (!path || typeof path !== 'string') {\n    return null;\n  }\n\n  // Check if CSS variables are used\n  if (obj && obj.vars && checkVars) {\n    const val = `vars.${path}`.split('.').reduce((acc, item) => acc && acc[item] ? acc[item] : null, obj);\n    if (val != null) {\n      return val;\n    }\n  }\n  return path.split('.').reduce((acc, item) => {\n    if (acc && acc[item] != null) {\n      return acc[item];\n    }\n    return null;\n  }, obj);\n}\nexport function getStyleValue(themeMapping, transform, propValueFinal, userValue = propValueFinal) {\n  let value;\n  if (typeof themeMapping === 'function') {\n    value = themeMapping(propValueFinal);\n  } else if (Array.isArray(themeMapping)) {\n    value = themeMapping[propValueFinal] || userValue;\n  } else {\n    value = getPath(themeMapping, propValueFinal) || userValue;\n  }\n  if (transform) {\n    value = transform(value, userValue, themeMapping);\n  }\n  return value;\n}\nfunction style(options) {\n  const {\n    prop,\n    cssProperty = options.prop,\n    themeKey,\n    transform\n  } = options;\n\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const fn = props => {\n    if (props[prop] == null) {\n      return null;\n    }\n    const propValue = props[prop];\n    const theme = props.theme;\n    const themeMapping = getPath(theme, themeKey) || {};\n    const styleFromPropValue = propValueFinal => {\n      let value = getStyleValue(themeMapping, transform, propValueFinal);\n      if (propValueFinal === value && typeof propValueFinal === 'string') {\n        // Haven't found value\n        value = getStyleValue(themeMapping, transform, `${prop}${propValueFinal === 'default' ? '' : capitalize(propValueFinal)}`, propValueFinal);\n      }\n      if (cssProperty === false) {\n        return value;\n      }\n      return {\n        [cssProperty]: value\n      };\n    };\n    return handleBreakpoints(props, propValue, styleFromPropValue);\n  };\n  fn.propTypes = process.env.NODE_ENV !== 'production' ? {\n    [prop]: responsivePropType\n  } : {};\n  fn.filterProps = [prop];\n  return fn;\n}\nexport default style;"], "names": [], "mappings": ";;;;;AAAA;AAEA;AADA;;;;AAEO,SAAS,QAAQ,GAAG,EAAE,IAAI,EAAE,YAAY,IAAI;IACjD,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO;IACT;IAEA,kCAAkC;IAClC,IAAI,OAAO,IAAI,IAAI,IAAI,WAAW;QAChC,MAAM,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,OAAO,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,MAAM;QACjG,IAAI,OAAO,MAAM;YACf,OAAO;QACT;IACF;IACA,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,KAAK;QAClC,IAAI,OAAO,GAAG,CAAC,KAAK,IAAI,MAAM;YAC5B,OAAO,GAAG,CAAC,KAAK;QAClB;QACA,OAAO;IACT,GAAG;AACL;AACO,SAAS,cAAc,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,YAAY,cAAc;IAC/F,IAAI;IACJ,IAAI,OAAO,iBAAiB,YAAY;QACtC,QAAQ,aAAa;IACvB,OAAO,IAAI,MAAM,OAAO,CAAC,eAAe;QACtC,QAAQ,YAAY,CAAC,eAAe,IAAI;IAC1C,OAAO;QACL,QAAQ,QAAQ,cAAc,mBAAmB;IACnD;IACA,IAAI,WAAW;QACb,QAAQ,UAAU,OAAO,WAAW;IACtC;IACA,OAAO;AACT;AACA,SAAS,MAAM,OAAO;IACpB,MAAM,EACJ,IAAI,EACJ,cAAc,QAAQ,IAAI,EAC1B,QAAQ,EACR,SAAS,EACV,GAAG;IAEJ,iBAAiB;IACjB,+DAA+D;IAC/D,MAAM,KAAK,CAAA;QACT,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM;YACvB,OAAO;QACT;QACA,MAAM,YAAY,KAAK,CAAC,KAAK;QAC7B,MAAM,QAAQ,MAAM,KAAK;QACzB,MAAM,eAAe,QAAQ,OAAO,aAAa,CAAC;QAClD,MAAM,qBAAqB,CAAA;YACzB,IAAI,QAAQ,cAAc,cAAc,WAAW;YACnD,IAAI,mBAAmB,SAAS,OAAO,mBAAmB,UAAU;gBAClE,sBAAsB;gBACtB,QAAQ,cAAc,cAAc,WAAW,GAAG,OAAO,mBAAmB,YAAY,KAAK,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD,EAAE,iBAAiB,EAAE;YAC7H;YACA,IAAI,gBAAgB,OAAO;gBACzB,OAAO;YACT;YACA,OAAO;gBACL,CAAC,YAAY,EAAE;YACjB;QACF;QACA,OAAO,CAAA,GAAA,oKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,WAAW;IAC7C;IACA,GAAG,SAAS,GAAG,uCAAwC;QACrD,CAAC,KAAK,EAAE,kLAAA,CAAA,UAAkB;IAC5B;IACA,GAAG,WAAW,GAAG;QAAC;KAAK;IACvB,OAAO;AACT;uCACe", "ignoreList": [0]}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/spacing/spacing.js"], "sourcesContent": ["import responsivePropType from \"../responsivePropType/index.js\";\nimport { handleBreakpoints } from \"../breakpoints/index.js\";\nimport { getPath } from \"../style/index.js\";\nimport merge from \"../merge/index.js\";\nimport memoize from \"../memoize/index.js\";\nconst properties = {\n  m: 'margin',\n  p: 'padding'\n};\nconst directions = {\n  t: 'Top',\n  r: 'Right',\n  b: 'Bottom',\n  l: 'Left',\n  x: ['Left', 'Right'],\n  y: ['Top', 'Bottom']\n};\nconst aliases = {\n  marginX: 'mx',\n  marginY: 'my',\n  paddingX: 'px',\n  paddingY: 'py'\n};\n\n// memoize() impact:\n// From 300,000 ops/sec\n// To 350,000 ops/sec\nconst getCssProperties = memoize(prop => {\n  // It's not a shorthand notation.\n  if (prop.length > 2) {\n    if (aliases[prop]) {\n      prop = aliases[prop];\n    } else {\n      return [prop];\n    }\n  }\n  const [a, b] = prop.split('');\n  const property = properties[a];\n  const direction = directions[b] || '';\n  return Array.isArray(direction) ? direction.map(dir => property + dir) : [property + direction];\n});\nexport const marginKeys = ['m', 'mt', 'mr', 'mb', 'ml', 'mx', 'my', 'margin', 'marginTop', 'marginRight', 'marginBottom', 'marginLeft', 'marginX', 'marginY', 'marginInline', 'marginInlineStart', 'marginInlineEnd', 'marginBlock', 'marginBlockStart', 'marginBlockEnd'];\nexport const paddingKeys = ['p', 'pt', 'pr', 'pb', 'pl', 'px', 'py', 'padding', 'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft', 'paddingX', 'paddingY', 'paddingInline', 'paddingInlineStart', 'paddingInlineEnd', 'paddingBlock', 'paddingBlockStart', 'paddingBlockEnd'];\nconst spacingKeys = [...marginKeys, ...paddingKeys];\nexport function createUnaryUnit(theme, themeKey, defaultValue, propName) {\n  const themeSpacing = getPath(theme, themeKey, true) ?? defaultValue;\n  if (typeof themeSpacing === 'number' || typeof themeSpacing === 'string') {\n    return val => {\n      if (typeof val === 'string') {\n        return val;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (typeof val !== 'number') {\n          console.error(`MUI: Expected ${propName} argument to be a number or a string, got ${val}.`);\n        }\n      }\n      if (typeof themeSpacing === 'string') {\n        return `calc(${val} * ${themeSpacing})`;\n      }\n      return themeSpacing * val;\n    };\n  }\n  if (Array.isArray(themeSpacing)) {\n    return val => {\n      if (typeof val === 'string') {\n        return val;\n      }\n      const abs = Math.abs(val);\n      if (process.env.NODE_ENV !== 'production') {\n        if (!Number.isInteger(abs)) {\n          console.error([`MUI: The \\`theme.${themeKey}\\` array type cannot be combined with non integer values.` + `You should either use an integer value that can be used as index, or define the \\`theme.${themeKey}\\` as a number.`].join('\\n'));\n        } else if (abs > themeSpacing.length - 1) {\n          console.error([`MUI: The value provided (${abs}) overflows.`, `The supported values are: ${JSON.stringify(themeSpacing)}.`, `${abs} > ${themeSpacing.length - 1}, you need to add the missing values.`].join('\\n'));\n        }\n      }\n      const transformed = themeSpacing[abs];\n      if (val >= 0) {\n        return transformed;\n      }\n      if (typeof transformed === 'number') {\n        return -transformed;\n      }\n      return `-${transformed}`;\n    };\n  }\n  if (typeof themeSpacing === 'function') {\n    return themeSpacing;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    console.error([`MUI: The \\`theme.${themeKey}\\` value (${themeSpacing}) is invalid.`, 'It should be a number, an array or a function.'].join('\\n'));\n  }\n  return () => undefined;\n}\nexport function createUnarySpacing(theme) {\n  return createUnaryUnit(theme, 'spacing', 8, 'spacing');\n}\nexport function getValue(transformer, propValue) {\n  if (typeof propValue === 'string' || propValue == null) {\n    return propValue;\n  }\n  return transformer(propValue);\n}\nexport function getStyleFromPropValue(cssProperties, transformer) {\n  return propValue => cssProperties.reduce((acc, cssProperty) => {\n    acc[cssProperty] = getValue(transformer, propValue);\n    return acc;\n  }, {});\n}\nfunction resolveCssProperty(props, keys, prop, transformer) {\n  // Using a hash computation over an array iteration could be faster, but with only 28 items,\n  // it's doesn't worth the bundle size.\n  if (!keys.includes(prop)) {\n    return null;\n  }\n  const cssProperties = getCssProperties(prop);\n  const styleFromPropValue = getStyleFromPropValue(cssProperties, transformer);\n  const propValue = props[prop];\n  return handleBreakpoints(props, propValue, styleFromPropValue);\n}\nfunction style(props, keys) {\n  const transformer = createUnarySpacing(props.theme);\n  return Object.keys(props).map(prop => resolveCssProperty(props, keys, prop, transformer)).reduce(merge, {});\n}\nexport function margin(props) {\n  return style(props, marginKeys);\n}\nmargin.propTypes = process.env.NODE_ENV !== 'production' ? marginKeys.reduce((obj, key) => {\n  obj[key] = responsivePropType;\n  return obj;\n}, {}) : {};\nmargin.filterProps = marginKeys;\nexport function padding(props) {\n  return style(props, paddingKeys);\n}\npadding.propTypes = process.env.NODE_ENV !== 'production' ? paddingKeys.reduce((obj, key) => {\n  obj[key] = responsivePropType;\n  return obj;\n}, {}) : {};\npadding.filterProps = paddingKeys;\nfunction spacing(props) {\n  return style(props, spacingKeys);\n}\nspacing.propTypes = process.env.NODE_ENV !== 'production' ? spacingKeys.reduce((obj, key) => {\n  obj[key] = responsivePropType;\n  return obj;\n}, {}) : {};\nspacing.filterProps = spacingKeys;\nexport default spacing;"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAIA;AAFA;AADA;AAEA;;;;;;AAEA,MAAM,aAAa;IACjB,GAAG;IACH,GAAG;AACL;AACA,MAAM,aAAa;IACjB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;QAAC;QAAQ;KAAQ;IACpB,GAAG;QAAC;QAAO;KAAS;AACtB;AACA,MAAM,UAAU;IACd,SAAS;IACT,SAAS;IACT,UAAU;IACV,UAAU;AACZ;AAEA,oBAAoB;AACpB,uBAAuB;AACvB,qBAAqB;AACrB,MAAM,mBAAmB,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE,CAAA;IAC/B,iCAAiC;IACjC,IAAI,KAAK,MAAM,GAAG,GAAG;QACnB,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,OAAO,OAAO,CAAC,KAAK;QACtB,OAAO;YACL,OAAO;gBAAC;aAAK;QACf;IACF;IACA,MAAM,CAAC,GAAG,EAAE,GAAG,KAAK,KAAK,CAAC;IAC1B,MAAM,WAAW,UAAU,CAAC,EAAE;IAC9B,MAAM,YAAY,UAAU,CAAC,EAAE,IAAI;IACnC,OAAO,MAAM,OAAO,CAAC,aAAa,UAAU,GAAG,CAAC,CAAA,MAAO,WAAW,OAAO;QAAC,WAAW;KAAU;AACjG;AACO,MAAM,aAAa;IAAC;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAU;IAAa;IAAe;IAAgB;IAAc;IAAW;IAAW;IAAgB;IAAqB;IAAmB;IAAe;IAAoB;CAAiB;AACnQ,MAAM,cAAc;IAAC;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAW;IAAc;IAAgB;IAAiB;IAAe;IAAY;IAAY;IAAiB;IAAsB;IAAoB;IAAgB;IAAqB;CAAkB;AACxR,MAAM,cAAc;OAAI;OAAe;CAAY;AAC5C,SAAS,gBAAgB,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ;IACrE,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,OAAO,UAAU,SAAS;IACvD,IAAI,OAAO,iBAAiB,YAAY,OAAO,iBAAiB,UAAU;QACxE,OAAO,CAAA;YACL,IAAI,OAAO,QAAQ,UAAU;gBAC3B,OAAO;YACT;YACA,wCAA2C;gBACzC,IAAI,OAAO,QAAQ,UAAU;oBAC3B,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,0CAA0C,EAAE,IAAI,CAAC,CAAC;gBAC5F;YACF;YACA,IAAI,OAAO,iBAAiB,UAAU;gBACpC,OAAO,CAAC,KAAK,EAAE,IAAI,GAAG,EAAE,aAAa,CAAC,CAAC;YACzC;YACA,OAAO,eAAe;QACxB;IACF;IACA,IAAI,MAAM,OAAO,CAAC,eAAe;QAC/B,OAAO,CAAA;YACL,IAAI,OAAO,QAAQ,UAAU;gBAC3B,OAAO;YACT;YACA,MAAM,MAAM,KAAK,GAAG,CAAC;YACrB,wCAA2C;gBACzC,IAAI,CAAC,OAAO,SAAS,CAAC,MAAM;oBAC1B,QAAQ,KAAK,CAAC;wBAAC,CAAC,iBAAiB,EAAE,SAAS,yDAAyD,CAAC,GAAG,CAAC,wFAAwF,EAAE,SAAS,eAAe,CAAC;qBAAC,CAAC,IAAI,CAAC;gBACtO,OAAO,IAAI,MAAM,aAAa,MAAM,GAAG,GAAG;oBACxC,QAAQ,KAAK,CAAC;wBAAC,CAAC,yBAAyB,EAAE,IAAI,YAAY,CAAC;wBAAE,CAAC,0BAA0B,EAAE,KAAK,SAAS,CAAC,cAAc,CAAC,CAAC;wBAAE,GAAG,IAAI,GAAG,EAAE,aAAa,MAAM,GAAG,EAAE,qCAAqC,CAAC;qBAAC,CAAC,IAAI,CAAC;gBAC/M;YACF;YACA,MAAM,cAAc,YAAY,CAAC,IAAI;YACrC,IAAI,OAAO,GAAG;gBACZ,OAAO;YACT;YACA,IAAI,OAAO,gBAAgB,UAAU;gBACnC,OAAO,CAAC;YACV;YACA,OAAO,CAAC,CAAC,EAAE,aAAa;QAC1B;IACF;IACA,IAAI,OAAO,iBAAiB,YAAY;QACtC,OAAO;IACT;IACA,wCAA2C;QACzC,QAAQ,KAAK,CAAC;YAAC,CAAC,iBAAiB,EAAE,SAAS,UAAU,EAAE,aAAa,aAAa,CAAC;YAAE;SAAiD,CAAC,IAAI,CAAC;IAC9I;IACA,OAAO,IAAM;AACf;AACO,SAAS,mBAAmB,KAAK;IACtC,OAAO,gBAAgB,OAAO,WAAW,GAAG;AAC9C;AACO,SAAS,SAAS,WAAW,EAAE,SAAS;IAC7C,IAAI,OAAO,cAAc,YAAY,aAAa,MAAM;QACtD,OAAO;IACT;IACA,OAAO,YAAY;AACrB;AACO,SAAS,sBAAsB,aAAa,EAAE,WAAW;IAC9D,OAAO,CAAA,YAAa,cAAc,MAAM,CAAC,CAAC,KAAK;YAC7C,GAAG,CAAC,YAAY,GAAG,SAAS,aAAa;YACzC,OAAO;QACT,GAAG,CAAC;AACN;AACA,SAAS,mBAAmB,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW;IACxD,4FAA4F;IAC5F,sCAAsC;IACtC,IAAI,CAAC,KAAK,QAAQ,CAAC,OAAO;QACxB,OAAO;IACT;IACA,MAAM,gBAAgB,iBAAiB;IACvC,MAAM,qBAAqB,sBAAsB,eAAe;IAChE,MAAM,YAAY,KAAK,CAAC,KAAK;IAC7B,OAAO,CAAA,GAAA,oKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,WAAW;AAC7C;AACA,SAAS,MAAM,KAAK,EAAE,IAAI;IACxB,MAAM,cAAc,mBAAmB,MAAM,KAAK;IAClD,OAAO,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA,OAAQ,mBAAmB,OAAO,MAAM,MAAM,cAAc,MAAM,CAAC,wJAAA,CAAA,UAAK,EAAE,CAAC;AAC3G;AACO,SAAS,OAAO,KAAK;IAC1B,OAAO,MAAM,OAAO;AACtB;AACA,OAAO,SAAS,GAAG,uCAAwC,WAAW,MAAM,CAAC,CAAC,KAAK;IACjF,GAAG,CAAC,IAAI,GAAG,kLAAA,CAAA,UAAkB;IAC7B,OAAO;AACT,GAAG,CAAC;AACJ,OAAO,WAAW,GAAG;AACd,SAAS,QAAQ,KAAK;IAC3B,OAAO,MAAM,OAAO;AACtB;AACA,QAAQ,SAAS,GAAG,uCAAwC,YAAY,MAAM,CAAC,CAAC,KAAK;IACnF,GAAG,CAAC,IAAI,GAAG,kLAAA,CAAA,UAAkB;IAC7B,OAAO;AACT,GAAG,CAAC;AACJ,QAAQ,WAAW,GAAG;AACtB,SAAS,QAAQ,KAAK;IACpB,OAAO,MAAM,OAAO;AACtB;AACA,QAAQ,SAAS,GAAG,uCAAwC,YAAY,MAAM,CAAC,CAAC,KAAK;IACnF,GAAG,CAAC,IAAI,GAAG,kLAAA,CAAA,UAAkB;IAC7B,OAAO;AACT,GAAG,CAAC;AACJ,QAAQ,WAAW,GAAG;uCACP", "ignoreList": [0]}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/createTheme/createSpacing.js"], "sourcesContent": ["import { createUnarySpacing } from \"../spacing/index.js\";\n\n// The different signatures imply different meaning for their arguments that can't be expressed structurally.\n// We express the difference with variable names.\n\nexport default function createSpacing(spacingInput = 8,\n// Material Design layouts are visually balanced. Most measurements align to an 8dp grid, which aligns both spacing and the overall layout.\n// Smaller components, such as icons, can align to a 4dp grid.\n// https://m2.material.io/design/layout/understanding-layout.html\ntransform = createUnarySpacing({\n  spacing: spacingInput\n})) {\n  // Already transformed.\n  if (spacingInput.mui) {\n    return spacingInput;\n  }\n  const spacing = (...argsInput) => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!(argsInput.length <= 4)) {\n        console.error(`MUI: Too many arguments provided, expected between 0 and 4, got ${argsInput.length}`);\n      }\n    }\n    const args = argsInput.length === 0 ? [1] : argsInput;\n    return args.map(argument => {\n      const output = transform(argument);\n      return typeof output === 'number' ? `${output}px` : output;\n    }).join(' ');\n  };\n  spacing.mui = true;\n  return spacing;\n}"], "names": [], "mappings": ";;;AAAA;;AAKe,SAAS,cAAc,eAAe,CAAC,EACtD,2IAA2I;AAC3I,8DAA8D;AAC9D,iEAAiE;AACjE,YAAY,CAAA,GAAA,4JAAA,CAAA,qBAAkB,AAAD,EAAE;IAC7B,SAAS;AACX,EAAE;IACA,uBAAuB;IACvB,IAAI,aAAa,GAAG,EAAE;QACpB,OAAO;IACT;IACA,MAAM,UAAU,CAAC,GAAG;QAClB,wCAA2C;YACzC,IAAI,CAAC,CAAC,UAAU,MAAM,IAAI,CAAC,GAAG;gBAC5B,QAAQ,KAAK,CAAC,CAAC,gEAAgE,EAAE,UAAU,MAAM,EAAE;YACrG;QACF;QACA,MAAM,OAAO,UAAU,MAAM,KAAK,IAAI;YAAC;SAAE,GAAG;QAC5C,OAAO,KAAK,GAAG,CAAC,CAAA;YACd,MAAM,SAAS,UAAU;YACzB,OAAO,OAAO,WAAW,WAAW,GAAG,OAAO,EAAE,CAAC,GAAG;QACtD,GAAG,IAAI,CAAC;IACV;IACA,QAAQ,GAAG,GAAG;IACd,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/createTheme/shape.js"], "sourcesContent": ["const shape = {\n  borderRadius: 4\n};\nexport default shape;"], "names": [], "mappings": ";;;AAAA,MAAM,QAAQ;IACZ,cAAc;AAChB;uCACe", "ignoreList": [0]}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/createTheme/applyStyles.js"], "sourcesContent": ["/**\n * A universal utility to style components with multiple color modes. Always use it from the theme object.\n * It works with:\n *  - [Basic theme](https://mui.com/material-ui/customization/dark-mode/)\n *  - [CSS theme variables](https://mui.com/material-ui/customization/css-theme-variables/overview/)\n *  - Zero-runtime engine\n *\n * Tips: Use an array over object spread and place `theme.applyStyles()` last.\n *\n * With the styled function:\n * ✅ [{ background: '#e5e5e5' }, theme.applyStyles('dark', { background: '#1c1c1c' })]\n * 🚫 { background: '#e5e5e5', ...theme.applyStyles('dark', { background: '#1c1c1c' })}\n *\n * With the sx prop:\n * ✅ [{ background: '#e5e5e5' }, theme => theme.applyStyles('dark', { background: '#1c1c1c' })]\n * 🚫 { background: '#e5e5e5', ...theme => theme.applyStyles('dark', { background: '#1c1c1c' })}\n *\n * @example\n * 1. using with `styled`:\n * ```jsx\n *   const Component = styled('div')(({ theme }) => [\n *     { background: '#e5e5e5' },\n *     theme.applyStyles('dark', {\n *       background: '#1c1c1c',\n *       color: '#fff',\n *     }),\n *   ]);\n * ```\n *\n * @example\n * 2. using with `sx` prop:\n * ```jsx\n *   <Box sx={[\n *     { background: '#e5e5e5' },\n *     theme => theme.applyStyles('dark', {\n *        background: '#1c1c1c',\n *        color: '#fff',\n *      }),\n *     ]}\n *   />\n * ```\n *\n * @example\n * 3. theming a component:\n * ```jsx\n *   extendTheme({\n *     components: {\n *       MuiButton: {\n *         styleOverrides: {\n *           root: ({ theme }) => [\n *             { background: '#e5e5e5' },\n *             theme.applyStyles('dark', {\n *               background: '#1c1c1c',\n *               color: '#fff',\n *             }),\n *           ],\n *         },\n *       }\n *     }\n *   })\n *```\n */\nexport default function applyStyles(key, styles) {\n  // @ts-expect-error this is 'any' type\n  const theme = this;\n  if (theme.vars) {\n    if (!theme.colorSchemes?.[key] || typeof theme.getColorSchemeSelector !== 'function') {\n      return {};\n    }\n    // If CssVarsProvider is used as a provider, returns '*:where({selector}) &'\n    let selector = theme.getColorSchemeSelector(key);\n    if (selector === '&') {\n      return styles;\n    }\n    if (selector.includes('data-') || selector.includes('.')) {\n      // '*' is required as a workaround for Emotion issue (https://github.com/emotion-js/emotion/issues/2836)\n      selector = `*:where(${selector.replace(/\\s*&$/, '')}) &`;\n    }\n    return {\n      [selector]: styles\n    };\n  }\n  if (theme.palette.mode === key) {\n    return styles;\n  }\n  return {};\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6DC;;;AACc,SAAS,YAAY,GAAG,EAAE,MAAM;IAC7C,sCAAsC;IACtC,MAAM,QAAQ,IAAI;IAClB,IAAI,MAAM,IAAI,EAAE;QACd,IAAI,CAAC,MAAM,YAAY,EAAE,CAAC,IAAI,IAAI,OAAO,MAAM,sBAAsB,KAAK,YAAY;YACpF,OAAO,CAAC;QACV;QACA,4EAA4E;QAC5E,IAAI,WAAW,MAAM,sBAAsB,CAAC;QAC5C,IAAI,aAAa,KAAK;YACpB,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,MAAM;YACxD,wGAAwG;YACxG,WAAW,CAAC,QAAQ,EAAE,SAAS,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC;QAC1D;QACA,OAAO;YACL,CAAC,SAAS,EAAE;QACd;IACF;IACA,IAAI,MAAM,OAAO,CAAC,IAAI,KAAK,KAAK;QAC9B,OAAO;IACT;IACA,OAAO,CAAC;AACV", "ignoreList": [0]}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/compose/compose.js"], "sourcesContent": ["import merge from \"../merge/index.js\";\nfunction compose(...styles) {\n  const handlers = styles.reduce((acc, style) => {\n    style.filterProps.forEach(prop => {\n      acc[prop] = style;\n    });\n    return acc;\n  }, {});\n\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const fn = props => {\n    return Object.keys(props).reduce((acc, prop) => {\n      if (handlers[prop]) {\n        return merge(acc, handlers[prop](props));\n      }\n      return acc;\n    }, {});\n  };\n  fn.propTypes = process.env.NODE_ENV !== 'production' ? styles.reduce((acc, style) => Object.assign(acc, style.propTypes), {}) : {};\n  fn.filterProps = styles.reduce((acc, style) => acc.concat(style.filterProps), []);\n  return fn;\n}\nexport default compose;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,GAAG,MAAM;IACxB,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,KAAK;QACnC,MAAM,WAAW,CAAC,OAAO,CAAC,CAAA;YACxB,GAAG,CAAC,KAAK,GAAG;QACd;QACA,OAAO;IACT,GAAG,CAAC;IAEJ,iBAAiB;IACjB,+DAA+D;IAC/D,MAAM,KAAK,CAAA;QACT,OAAO,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACrC,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE,KAAK,QAAQ,CAAC,KAAK,CAAC;YACnC;YACA,OAAO;QACT,GAAG,CAAC;IACN;IACA,GAAG,SAAS,GAAG,uCAAwC,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,OAAO,MAAM,CAAC,KAAK,MAAM,SAAS,GAAG,CAAC;IAC3H,GAAG,WAAW,GAAG,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,IAAI,MAAM,CAAC,MAAM,WAAW,GAAG,EAAE;IAChF,OAAO;AACT;uCACe", "ignoreList": [0]}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/borders/borders.js"], "sourcesContent": ["import responsivePropType from \"../responsivePropType/index.js\";\nimport style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nimport { createUnaryUnit, getValue } from \"../spacing/index.js\";\nimport { handleBreakpoints } from \"../breakpoints/index.js\";\nexport function borderTransform(value) {\n  if (typeof value !== 'number') {\n    return value;\n  }\n  return `${value}px solid`;\n}\nfunction createBorderStyle(prop, transform) {\n  return style({\n    prop,\n    themeKey: 'borders',\n    transform\n  });\n}\nexport const border = createBorderStyle('border', borderTransform);\nexport const borderTop = createBorderStyle('borderTop', borderTransform);\nexport const borderRight = createBorderStyle('borderRight', borderTransform);\nexport const borderBottom = createBorderStyle('borderBottom', borderTransform);\nexport const borderLeft = createBorderStyle('borderLeft', borderTransform);\nexport const borderColor = createBorderStyle('borderColor');\nexport const borderTopColor = createBorderStyle('borderTopColor');\nexport const borderRightColor = createBorderStyle('borderRightColor');\nexport const borderBottomColor = createBorderStyle('borderBottomColor');\nexport const borderLeftColor = createBorderStyle('borderLeftColor');\nexport const outline = createBorderStyle('outline', borderTransform);\nexport const outlineColor = createBorderStyle('outlineColor');\n\n// false positive\n// eslint-disable-next-line react/function-component-definition\nexport const borderRadius = props => {\n  if (props.borderRadius !== undefined && props.borderRadius !== null) {\n    const transformer = createUnaryUnit(props.theme, 'shape.borderRadius', 4, 'borderRadius');\n    const styleFromPropValue = propValue => ({\n      borderRadius: getValue(transformer, propValue)\n    });\n    return handleBreakpoints(props, props.borderRadius, styleFromPropValue);\n  }\n  return null;\n};\nborderRadius.propTypes = process.env.NODE_ENV !== 'production' ? {\n  borderRadius: responsivePropType\n} : {};\nborderRadius.filterProps = ['borderRadius'];\nconst borders = compose(border, borderTop, borderRight, borderBottom, borderLeft, borderColor, borderTopColor, borderRightColor, borderBottomColor, borderLeftColor, borderRadius, outline, outlineColor);\nexport default borders;"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAGA;AACA;AAJA;AAEA;AADA;;;;;;AAIO,SAAS,gBAAgB,KAAK;IACnC,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO,GAAG,MAAM,QAAQ,CAAC;AAC3B;AACA,SAAS,kBAAkB,IAAI,EAAE,SAAS;IACxC,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;QACX;QACA,UAAU;QACV;IACF;AACF;AACO,MAAM,SAAS,kBAAkB,UAAU;AAC3C,MAAM,YAAY,kBAAkB,aAAa;AACjD,MAAM,cAAc,kBAAkB,eAAe;AACrD,MAAM,eAAe,kBAAkB,gBAAgB;AACvD,MAAM,aAAa,kBAAkB,cAAc;AACnD,MAAM,cAAc,kBAAkB;AACtC,MAAM,iBAAiB,kBAAkB;AACzC,MAAM,mBAAmB,kBAAkB;AAC3C,MAAM,oBAAoB,kBAAkB;AAC5C,MAAM,kBAAkB,kBAAkB;AAC1C,MAAM,UAAU,kBAAkB,WAAW;AAC7C,MAAM,eAAe,kBAAkB;AAIvC,MAAM,eAAe,CAAA;IAC1B,IAAI,MAAM,YAAY,KAAK,aAAa,MAAM,YAAY,KAAK,MAAM;QACnE,MAAM,cAAc,CAAA,GAAA,4JAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,KAAK,EAAE,sBAAsB,GAAG;QAC1E,MAAM,qBAAqB,CAAA,YAAa,CAAC;gBACvC,cAAc,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YACtC,CAAC;QACD,OAAO,CAAA,GAAA,oKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM,YAAY,EAAE;IACtD;IACA,OAAO;AACT;AACA,aAAa,SAAS,GAAG,uCAAwC;IAC/D,cAAc,kLAAA,CAAA,UAAkB;AAClC;AACA,aAAa,WAAW,GAAG;IAAC;CAAe;AAC3C,MAAM,UAAU,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,WAAW,aAAa,cAAc,YAAY,aAAa,gBAAgB,kBAAkB,mBAAmB,iBAAiB,cAAc,SAAS;uCAC7K", "ignoreList": [0]}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/palette/palette.js"], "sourcesContent": ["import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nexport function paletteTransform(value, userValue) {\n  if (userValue === 'grey') {\n    return userValue;\n  }\n  return value;\n}\nexport const color = style({\n  prop: 'color',\n  themeKey: 'palette',\n  transform: paletteTransform\n});\nexport const bgcolor = style({\n  prop: 'bgcolor',\n  cssProperty: 'backgroundColor',\n  themeKey: 'palette',\n  transform: paletteTransform\n});\nexport const backgroundColor = style({\n  prop: 'backgroundColor',\n  themeKey: 'palette',\n  transform: paletteTransform\n});\nconst palette = compose(color, bgcolor, backgroundColor);\nexport default palette;"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AACO,SAAS,iBAAiB,KAAK,EAAE,SAAS;IAC/C,IAAI,cAAc,QAAQ;QACxB,OAAO;IACT;IACA,OAAO;AACT;AACO,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IACzB,MAAM;IACN,UAAU;IACV,WAAW;AACb;AACO,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC3B,MAAM;IACN,aAAa;IACb,UAAU;IACV,WAAW;AACb;AACO,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IACnC,MAAM;IACN,UAAU;IACV,WAAW;AACb;AACA,MAAM,UAAU,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE,OAAO,SAAS;uCACzB", "ignoreList": [0]}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1035, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/cssGrid/cssGrid.js"], "sourcesContent": ["import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nimport { createUnaryUnit, getValue } from \"../spacing/index.js\";\nimport { handleBreakpoints } from \"../breakpoints/index.js\";\nimport responsivePropType from \"../responsivePropType/index.js\";\n\n// false positive\n// eslint-disable-next-line react/function-component-definition\nexport const gap = props => {\n  if (props.gap !== undefined && props.gap !== null) {\n    const transformer = createUnaryUnit(props.theme, 'spacing', 8, 'gap');\n    const styleFromPropValue = propValue => ({\n      gap: getValue(transformer, propValue)\n    });\n    return handleBreakpoints(props, props.gap, styleFromPropValue);\n  }\n  return null;\n};\ngap.propTypes = process.env.NODE_ENV !== 'production' ? {\n  gap: responsivePropType\n} : {};\ngap.filterProps = ['gap'];\n\n// false positive\n// eslint-disable-next-line react/function-component-definition\nexport const columnGap = props => {\n  if (props.columnGap !== undefined && props.columnGap !== null) {\n    const transformer = createUnaryUnit(props.theme, 'spacing', 8, 'columnGap');\n    const styleFromPropValue = propValue => ({\n      columnGap: getValue(transformer, propValue)\n    });\n    return handleBreakpoints(props, props.columnGap, styleFromPropValue);\n  }\n  return null;\n};\ncolumnGap.propTypes = process.env.NODE_ENV !== 'production' ? {\n  columnGap: responsivePropType\n} : {};\ncolumnGap.filterProps = ['columnGap'];\n\n// false positive\n// eslint-disable-next-line react/function-component-definition\nexport const rowGap = props => {\n  if (props.rowGap !== undefined && props.rowGap !== null) {\n    const transformer = createUnaryUnit(props.theme, 'spacing', 8, 'rowGap');\n    const styleFromPropValue = propValue => ({\n      rowGap: getValue(transformer, propValue)\n    });\n    return handleBreakpoints(props, props.rowGap, styleFromPropValue);\n  }\n  return null;\n};\nrowGap.propTypes = process.env.NODE_ENV !== 'production' ? {\n  rowGap: responsivePropType\n} : {};\nrowGap.filterProps = ['rowGap'];\nexport const gridColumn = style({\n  prop: 'gridColumn'\n});\nexport const gridRow = style({\n  prop: 'gridRow'\n});\nexport const gridAutoFlow = style({\n  prop: 'gridAutoFlow'\n});\nexport const gridAutoColumns = style({\n  prop: 'gridAutoColumns'\n});\nexport const gridAutoRows = style({\n  prop: 'gridAutoRows'\n});\nexport const gridTemplateColumns = style({\n  prop: 'gridTemplateColumns'\n});\nexport const gridTemplateRows = style({\n  prop: 'gridTemplateRows'\n});\nexport const gridTemplateAreas = style({\n  prop: 'gridTemplateAreas'\n});\nexport const gridArea = style({\n  prop: 'gridArea'\n});\nconst grid = compose(gap, columnGap, rowGap, gridColumn, gridRow, gridAutoFlow, gridAutoColumns, gridAutoRows, gridTemplateColumns, gridTemplateRows, gridTemplateAreas, gridArea);\nexport default grid;"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA;AACA;AACA;AAJA;AACA;;;;;;AAOO,MAAM,MAAM,CAAA;IACjB,IAAI,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK,MAAM;QACjD,MAAM,cAAc,CAAA,GAAA,4JAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,KAAK,EAAE,WAAW,GAAG;QAC/D,MAAM,qBAAqB,CAAA,YAAa,CAAC;gBACvC,KAAK,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YAC7B,CAAC;QACD,OAAO,CAAA,GAAA,oKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM,GAAG,EAAE;IAC7C;IACA,OAAO;AACT;AACA,IAAI,SAAS,GAAG,uCAAwC;IACtD,KAAK,kLAAA,CAAA,UAAkB;AACzB;AACA,IAAI,WAAW,GAAG;IAAC;CAAM;AAIlB,MAAM,YAAY,CAAA;IACvB,IAAI,MAAM,SAAS,KAAK,aAAa,MAAM,SAAS,KAAK,MAAM;QAC7D,MAAM,cAAc,CAAA,GAAA,4JAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,KAAK,EAAE,WAAW,GAAG;QAC/D,MAAM,qBAAqB,CAAA,YAAa,CAAC;gBACvC,WAAW,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YACnC,CAAC;QACD,OAAO,CAAA,GAAA,oKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM,SAAS,EAAE;IACnD;IACA,OAAO;AACT;AACA,UAAU,SAAS,GAAG,uCAAwC;IAC5D,WAAW,kLAAA,CAAA,UAAkB;AAC/B;AACA,UAAU,WAAW,GAAG;IAAC;CAAY;AAI9B,MAAM,SAAS,CAAA;IACpB,IAAI,MAAM,MAAM,KAAK,aAAa,MAAM,MAAM,KAAK,MAAM;QACvD,MAAM,cAAc,CAAA,GAAA,4JAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,KAAK,EAAE,WAAW,GAAG;QAC/D,MAAM,qBAAqB,CAAA,YAAa,CAAC;gBACvC,QAAQ,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YAChC,CAAC;QACD,OAAO,CAAA,GAAA,oKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM,MAAM,EAAE;IAChD;IACA,OAAO;AACT;AACA,OAAO,SAAS,GAAG,uCAAwC;IACzD,QAAQ,kLAAA,CAAA,UAAkB;AAC5B;AACA,OAAO,WAAW,GAAG;IAAC;CAAS;AACxB,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC9B,MAAM;AACR;AACO,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC3B,MAAM;AACR;AACO,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAChC,MAAM;AACR;AACO,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IACnC,MAAM;AACR;AACO,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAChC,MAAM;AACR;AACO,MAAM,sBAAsB,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IACvC,MAAM;AACR;AACO,MAAM,mBAAmB,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IACpC,MAAM;AACR;AACO,MAAM,oBAAoB,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IACrC,MAAM;AACR;AACO,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC5B,MAAM;AACR;AACA,MAAM,OAAO,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE,KAAK,WAAW,QAAQ,YAAY,SAAS,cAAc,iBAAiB,cAAc,qBAAqB,kBAAkB,mBAAmB;uCAC1J", "ignoreList": [0]}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1143, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/sizing/sizing.js"], "sourcesContent": ["import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nimport { handleBreakpoints, values as breakpointsValues } from \"../breakpoints/index.js\";\nexport function sizingTransform(value) {\n  return value <= 1 && value !== 0 ? `${value * 100}%` : value;\n}\nexport const width = style({\n  prop: 'width',\n  transform: sizingTransform\n});\nexport const maxWidth = props => {\n  if (props.maxWidth !== undefined && props.maxWidth !== null) {\n    const styleFromPropValue = propValue => {\n      const breakpoint = props.theme?.breakpoints?.values?.[propValue] || breakpointsValues[propValue];\n      if (!breakpoint) {\n        return {\n          maxWidth: sizingTransform(propValue)\n        };\n      }\n      if (props.theme?.breakpoints?.unit !== 'px') {\n        return {\n          maxWidth: `${breakpoint}${props.theme.breakpoints.unit}`\n        };\n      }\n      return {\n        maxWidth: breakpoint\n      };\n    };\n    return handleBreakpoints(props, props.maxWidth, styleFromPropValue);\n  }\n  return null;\n};\nmaxWidth.filterProps = ['maxWidth'];\nexport const minWidth = style({\n  prop: 'minWidth',\n  transform: sizingTransform\n});\nexport const height = style({\n  prop: 'height',\n  transform: sizingTransform\n});\nexport const maxHeight = style({\n  prop: 'maxHeight',\n  transform: sizingTransform\n});\nexport const minHeight = style({\n  prop: 'minHeight',\n  transform: sizingTransform\n});\nexport const sizeWidth = style({\n  prop: 'size',\n  cssProperty: 'width',\n  transform: sizingTransform\n});\nexport const sizeHeight = style({\n  prop: 'size',\n  cssProperty: 'height',\n  transform: sizingTransform\n});\nexport const boxSizing = style({\n  prop: 'boxSizing'\n});\nconst sizing = compose(width, maxWidth, minWidth, height, maxHeight, minHeight, boxSizing);\nexport default sizing;"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAEA;AADA;;;;AAEO,SAAS,gBAAgB,KAAK;IACnC,OAAO,SAAS,KAAK,UAAU,IAAI,GAAG,QAAQ,IAAI,CAAC,CAAC,GAAG;AACzD;AACO,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IACzB,MAAM;IACN,WAAW;AACb;AACO,MAAM,WAAW,CAAA;IACtB,IAAI,MAAM,QAAQ,KAAK,aAAa,MAAM,QAAQ,KAAK,MAAM;QAC3D,MAAM,qBAAqB,CAAA;YACzB,MAAM,aAAa,MAAM,KAAK,EAAE,aAAa,QAAQ,CAAC,UAAU,IAAI,oKAAA,CAAA,SAAiB,CAAC,UAAU;YAChG,IAAI,CAAC,YAAY;gBACf,OAAO;oBACL,UAAU,gBAAgB;gBAC5B;YACF;YACA,IAAI,MAAM,KAAK,EAAE,aAAa,SAAS,MAAM;gBAC3C,OAAO;oBACL,UAAU,GAAG,aAAa,MAAM,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE;gBAC1D;YACF;YACA,OAAO;gBACL,UAAU;YACZ;QACF;QACA,OAAO,CAAA,GAAA,oKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM,QAAQ,EAAE;IAClD;IACA,OAAO;AACT;AACA,SAAS,WAAW,GAAG;IAAC;CAAW;AAC5B,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC5B,MAAM;IACN,WAAW;AACb;AACO,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC1B,MAAM;IACN,WAAW;AACb;AACO,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC7B,MAAM;IACN,WAAW;AACb;AACO,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC7B,MAAM;IACN,WAAW;AACb;AACO,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC7B,MAAM;IACN,aAAa;IACb,WAAW;AACb;AACO,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC9B,MAAM;IACN,aAAa;IACb,WAAW;AACb;AACO,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE;IAC7B,MAAM;AACR;AACA,MAAM,SAAS,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE,OAAO,UAAU,UAAU,QAAQ,WAAW,WAAW;uCACjE", "ignoreList": [0]}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/styleFunctionSx/defaultSxConfig.js"], "sourcesContent": ["import { padding, margin } from \"../spacing/index.js\";\nimport { borderRadius, borderTransform } from \"../borders/index.js\";\nimport { gap, rowGap, columnGap } from \"../cssGrid/index.js\";\nimport { paletteTransform } from \"../palette/index.js\";\nimport { maxWidth, sizingTransform } from \"../sizing/index.js\";\nconst defaultSxConfig = {\n  // borders\n  border: {\n    themeKey: 'borders',\n    transform: borderTransform\n  },\n  borderTop: {\n    themeKey: 'borders',\n    transform: borderTransform\n  },\n  borderRight: {\n    themeKey: 'borders',\n    transform: borderTransform\n  },\n  borderBottom: {\n    themeKey: 'borders',\n    transform: borderTransform\n  },\n  borderLeft: {\n    themeKey: 'borders',\n    transform: borderTransform\n  },\n  borderColor: {\n    themeKey: 'palette'\n  },\n  borderTopColor: {\n    themeKey: 'palette'\n  },\n  borderRightColor: {\n    themeKey: 'palette'\n  },\n  borderBottomColor: {\n    themeKey: 'palette'\n  },\n  borderLeftColor: {\n    themeKey: 'palette'\n  },\n  outline: {\n    themeKey: 'borders',\n    transform: borderTransform\n  },\n  outlineColor: {\n    themeKey: 'palette'\n  },\n  borderRadius: {\n    themeKey: 'shape.borderRadius',\n    style: borderRadius\n  },\n  // palette\n  color: {\n    themeKey: 'palette',\n    transform: paletteTransform\n  },\n  bgcolor: {\n    themeKey: 'palette',\n    cssProperty: 'backgroundColor',\n    transform: paletteTransform\n  },\n  backgroundColor: {\n    themeKey: 'palette',\n    transform: paletteTransform\n  },\n  // spacing\n  p: {\n    style: padding\n  },\n  pt: {\n    style: padding\n  },\n  pr: {\n    style: padding\n  },\n  pb: {\n    style: padding\n  },\n  pl: {\n    style: padding\n  },\n  px: {\n    style: padding\n  },\n  py: {\n    style: padding\n  },\n  padding: {\n    style: padding\n  },\n  paddingTop: {\n    style: padding\n  },\n  paddingRight: {\n    style: padding\n  },\n  paddingBottom: {\n    style: padding\n  },\n  paddingLeft: {\n    style: padding\n  },\n  paddingX: {\n    style: padding\n  },\n  paddingY: {\n    style: padding\n  },\n  paddingInline: {\n    style: padding\n  },\n  paddingInlineStart: {\n    style: padding\n  },\n  paddingInlineEnd: {\n    style: padding\n  },\n  paddingBlock: {\n    style: padding\n  },\n  paddingBlockStart: {\n    style: padding\n  },\n  paddingBlockEnd: {\n    style: padding\n  },\n  m: {\n    style: margin\n  },\n  mt: {\n    style: margin\n  },\n  mr: {\n    style: margin\n  },\n  mb: {\n    style: margin\n  },\n  ml: {\n    style: margin\n  },\n  mx: {\n    style: margin\n  },\n  my: {\n    style: margin\n  },\n  margin: {\n    style: margin\n  },\n  marginTop: {\n    style: margin\n  },\n  marginRight: {\n    style: margin\n  },\n  marginBottom: {\n    style: margin\n  },\n  marginLeft: {\n    style: margin\n  },\n  marginX: {\n    style: margin\n  },\n  marginY: {\n    style: margin\n  },\n  marginInline: {\n    style: margin\n  },\n  marginInlineStart: {\n    style: margin\n  },\n  marginInlineEnd: {\n    style: margin\n  },\n  marginBlock: {\n    style: margin\n  },\n  marginBlockStart: {\n    style: margin\n  },\n  marginBlockEnd: {\n    style: margin\n  },\n  // display\n  displayPrint: {\n    cssProperty: false,\n    transform: value => ({\n      '@media print': {\n        display: value\n      }\n    })\n  },\n  display: {},\n  overflow: {},\n  textOverflow: {},\n  visibility: {},\n  whiteSpace: {},\n  // flexbox\n  flexBasis: {},\n  flexDirection: {},\n  flexWrap: {},\n  justifyContent: {},\n  alignItems: {},\n  alignContent: {},\n  order: {},\n  flex: {},\n  flexGrow: {},\n  flexShrink: {},\n  alignSelf: {},\n  justifyItems: {},\n  justifySelf: {},\n  // grid\n  gap: {\n    style: gap\n  },\n  rowGap: {\n    style: rowGap\n  },\n  columnGap: {\n    style: columnGap\n  },\n  gridColumn: {},\n  gridRow: {},\n  gridAutoFlow: {},\n  gridAutoColumns: {},\n  gridAutoRows: {},\n  gridTemplateColumns: {},\n  gridTemplateRows: {},\n  gridTemplateAreas: {},\n  gridArea: {},\n  // positions\n  position: {},\n  zIndex: {\n    themeKey: 'zIndex'\n  },\n  top: {},\n  right: {},\n  bottom: {},\n  left: {},\n  // shadows\n  boxShadow: {\n    themeKey: 'shadows'\n  },\n  // sizing\n  width: {\n    transform: sizingTransform\n  },\n  maxWidth: {\n    style: maxWidth\n  },\n  minWidth: {\n    transform: sizingTransform\n  },\n  height: {\n    transform: sizingTransform\n  },\n  maxHeight: {\n    transform: sizingTransform\n  },\n  minHeight: {\n    transform: sizingTransform\n  },\n  boxSizing: {},\n  // typography\n  font: {\n    themeKey: 'font'\n  },\n  fontFamily: {\n    themeKey: 'typography'\n  },\n  fontSize: {\n    themeKey: 'typography'\n  },\n  fontStyle: {\n    themeKey: 'typography'\n  },\n  fontWeight: {\n    themeKey: 'typography'\n  },\n  letterSpacing: {},\n  textTransform: {},\n  lineHeight: {},\n  textAlign: {},\n  typography: {\n    cssProperty: false,\n    themeKey: 'typography'\n  }\n};\nexport default defaultSxConfig;"], "names": [], "mappings": ";;;AACA;AAEA;AAHA;AAEA;AAEA;;;;;;AACA,MAAM,kBAAkB;IACtB,UAAU;IACV,QAAQ;QACN,UAAU;QACV,WAAW,4JAAA,CAAA,kBAAe;IAC5B;IACA,WAAW;QACT,UAAU;QACV,WAAW,4JAAA,CAAA,kBAAe;IAC5B;IACA,aAAa;QACX,UAAU;QACV,WAAW,4JAAA,CAAA,kBAAe;IAC5B;IACA,cAAc;QACZ,UAAU;QACV,WAAW,4JAAA,CAAA,kBAAe;IAC5B;IACA,YAAY;QACV,UAAU;QACV,WAAW,4JAAA,CAAA,kBAAe;IAC5B;IACA,aAAa;QACX,UAAU;IACZ;IACA,gBAAgB;QACd,UAAU;IACZ;IACA,kBAAkB;QAChB,UAAU;IACZ;IACA,mBAAmB;QACjB,UAAU;IACZ;IACA,iBAAiB;QACf,UAAU;IACZ;IACA,SAAS;QACP,UAAU;QACV,WAAW,4JAAA,CAAA,kBAAe;IAC5B;IACA,cAAc;QACZ,UAAU;IACZ;IACA,cAAc;QACZ,UAAU;QACV,OAAO,4JAAA,CAAA,eAAY;IACrB;IACA,UAAU;IACV,OAAO;QACL,UAAU;QACV,WAAW,4JAAA,CAAA,mBAAgB;IAC7B;IACA,SAAS;QACP,UAAU;QACV,aAAa;QACb,WAAW,4JAAA,CAAA,mBAAgB;IAC7B;IACA,iBAAiB;QACf,UAAU;QACV,WAAW,4JAAA,CAAA,mBAAgB;IAC7B;IACA,UAAU;IACV,GAAG;QACD,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,SAAS;QACP,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,YAAY;QACV,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,cAAc;QACZ,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,eAAe;QACb,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,aAAa;QACX,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,UAAU;QACR,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,UAAU;QACR,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,eAAe;QACb,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,oBAAoB;QAClB,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,kBAAkB;QAChB,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,cAAc;QACZ,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,mBAAmB;QACjB,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,iBAAiB;QACf,OAAO,4JAAA,CAAA,UAAO;IAChB;IACA,GAAG;QACD,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,IAAI;QACF,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,QAAQ;QACN,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,WAAW;QACT,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,aAAa;QACX,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,cAAc;QACZ,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,YAAY;QACV,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,SAAS;QACP,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,SAAS;QACP,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,cAAc;QACZ,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,mBAAmB;QACjB,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,iBAAiB;QACf,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,aAAa;QACX,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,kBAAkB;QAChB,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,gBAAgB;QACd,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,UAAU;IACV,cAAc;QACZ,aAAa;QACb,WAAW,CAAA,QAAS,CAAC;gBACnB,gBAAgB;oBACd,SAAS;gBACX;YACF,CAAC;IACH;IACA,SAAS,CAAC;IACV,UAAU,CAAC;IACX,cAAc,CAAC;IACf,YAAY,CAAC;IACb,YAAY,CAAC;IACb,UAAU;IACV,WAAW,CAAC;IACZ,eAAe,CAAC;IAChB,UAAU,CAAC;IACX,gBAAgB,CAAC;IACjB,YAAY,CAAC;IACb,cAAc,CAAC;IACf,OAAO,CAAC;IACR,MAAM,CAAC;IACP,UAAU,CAAC;IACX,YAAY,CAAC;IACb,WAAW,CAAC;IACZ,cAAc,CAAC;IACf,aAAa,CAAC;IACd,OAAO;IACP,KAAK;QACH,OAAO,4JAAA,CAAA,MAAG;IACZ;IACA,QAAQ;QACN,OAAO,4JAAA,CAAA,SAAM;IACf;IACA,WAAW;QACT,OAAO,4JAAA,CAAA,YAAS;IAClB;IACA,YAAY,CAAC;IACb,SAAS,CAAC;IACV,cAAc,CAAC;IACf,iBAAiB,CAAC;IAClB,cAAc,CAAC;IACf,qBAAqB,CAAC;IACtB,kBAAkB,CAAC;IACnB,mBAAmB,CAAC;IACpB,UAAU,CAAC;IACX,YAAY;IACZ,UAAU,CAAC;IACX,QAAQ;QACN,UAAU;IACZ;IACA,KAAK,CAAC;IACN,OAAO,CAAC;IACR,QAAQ,CAAC;IACT,MAAM,CAAC;IACP,UAAU;IACV,WAAW;QACT,UAAU;IACZ;IACA,SAAS;IACT,OAAO;QACL,WAAW,0JAAA,CAAA,kBAAe;IAC5B;IACA,UAAU;QACR,OAAO,0JAAA,CAAA,WAAQ;IACjB;IACA,UAAU;QACR,WAAW,0JAAA,CAAA,kBAAe;IAC5B;IACA,QAAQ;QACN,WAAW,0JAAA,CAAA,kBAAe;IAC5B;IACA,WAAW;QACT,WAAW,0JAAA,CAAA,kBAAe;IAC5B;IACA,WAAW;QACT,WAAW,0JAAA,CAAA,kBAAe;IAC5B;IACA,WAAW,CAAC;IACZ,aAAa;IACb,MAAM;QACJ,UAAU;IACZ;IACA,YAAY;QACV,UAAU;IACZ;IACA,UAAU;QACR,UAAU;IACZ;IACA,WAAW;QACT,UAAU;IACZ;IACA,YAAY;QACV,UAAU;IACZ;IACA,eAAe,CAAC;IAChB,eAAe,CAAC;IAChB,YAAY,CAAC;IACb,WAAW,CAAC;IACZ,YAAY;QACV,aAAa;QACb,UAAU;IACZ;AACF;uCACe", "ignoreList": [0]}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1539, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/styleFunctionSx/styleFunctionSx.js"], "sourcesContent": ["import capitalize from '@mui/utils/capitalize';\nimport merge from \"../merge/index.js\";\nimport { getPath, getStyleValue as getValue } from \"../style/index.js\";\nimport { handleBreakpoints, createEmptyBreakpointObject, removeUnusedBreakpoints } from \"../breakpoints/index.js\";\nimport { sortContainerQueries } from \"../cssContainerQueries/index.js\";\nimport defaultSxConfig from \"./defaultSxConfig.js\";\nfunction objectsHaveSameKeys(...objects) {\n  const allKeys = objects.reduce((keys, object) => keys.concat(Object.keys(object)), []);\n  const union = new Set(allKeys);\n  return objects.every(object => union.size === Object.keys(object).length);\n}\nfunction callIfFn(maybeFn, arg) {\n  return typeof maybeFn === 'function' ? maybeFn(arg) : maybeFn;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_createStyleFunctionSx() {\n  function getThemeValue(prop, val, theme, config) {\n    const props = {\n      [prop]: val,\n      theme\n    };\n    const options = config[prop];\n    if (!options) {\n      return {\n        [prop]: val\n      };\n    }\n    const {\n      cssProperty = prop,\n      themeKey,\n      transform,\n      style\n    } = options;\n    if (val == null) {\n      return null;\n    }\n\n    // TODO v6: remove, see https://github.com/mui/material-ui/pull/38123\n    if (themeKey === 'typography' && val === 'inherit') {\n      return {\n        [prop]: val\n      };\n    }\n    const themeMapping = getPath(theme, themeKey) || {};\n    if (style) {\n      return style(props);\n    }\n    const styleFromPropValue = propValueFinal => {\n      let value = getValue(themeMapping, transform, propValueFinal);\n      if (propValueFinal === value && typeof propValueFinal === 'string') {\n        // Haven't found value\n        value = getValue(themeMapping, transform, `${prop}${propValueFinal === 'default' ? '' : capitalize(propValueFinal)}`, propValueFinal);\n      }\n      if (cssProperty === false) {\n        return value;\n      }\n      return {\n        [cssProperty]: value\n      };\n    };\n    return handleBreakpoints(props, val, styleFromPropValue);\n  }\n  function styleFunctionSx(props) {\n    const {\n      sx,\n      theme = {}\n    } = props || {};\n    if (!sx) {\n      return null; // Emotion & styled-components will neglect null\n    }\n    const config = theme.unstable_sxConfig ?? defaultSxConfig;\n\n    /*\n     * Receive `sxInput` as object or callback\n     * and then recursively check keys & values to create media query object styles.\n     * (the result will be used in `styled`)\n     */\n    function traverse(sxInput) {\n      let sxObject = sxInput;\n      if (typeof sxInput === 'function') {\n        sxObject = sxInput(theme);\n      } else if (typeof sxInput !== 'object') {\n        // value\n        return sxInput;\n      }\n      if (!sxObject) {\n        return null;\n      }\n      const emptyBreakpoints = createEmptyBreakpointObject(theme.breakpoints);\n      const breakpointsKeys = Object.keys(emptyBreakpoints);\n      let css = emptyBreakpoints;\n      Object.keys(sxObject).forEach(styleKey => {\n        const value = callIfFn(sxObject[styleKey], theme);\n        if (value !== null && value !== undefined) {\n          if (typeof value === 'object') {\n            if (config[styleKey]) {\n              css = merge(css, getThemeValue(styleKey, value, theme, config));\n            } else {\n              const breakpointsValues = handleBreakpoints({\n                theme\n              }, value, x => ({\n                [styleKey]: x\n              }));\n              if (objectsHaveSameKeys(breakpointsValues, value)) {\n                css[styleKey] = styleFunctionSx({\n                  sx: value,\n                  theme\n                });\n              } else {\n                css = merge(css, breakpointsValues);\n              }\n            }\n          } else {\n            css = merge(css, getThemeValue(styleKey, value, theme, config));\n          }\n        }\n      });\n      return sortContainerQueries(theme, removeUnusedBreakpoints(breakpointsKeys, css));\n    }\n    return Array.isArray(sx) ? sx.map(traverse) : traverse(sx);\n  }\n  return styleFunctionSx;\n}\nconst styleFunctionSx = unstable_createStyleFunctionSx();\nstyleFunctionSx.filterProps = ['sx'];\nexport default styleFunctionSx;"], "names": [], "mappings": ";;;;AAEA;AAFA;AAGA;AAEA;AAJA;AAGA;;;;;;;AAEA,SAAS,oBAAoB,GAAG,OAAO;IACrC,MAAM,UAAU,QAAQ,MAAM,CAAC,CAAC,MAAM,SAAW,KAAK,MAAM,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE;IACrF,MAAM,QAAQ,IAAI,IAAI;IACtB,OAAO,QAAQ,KAAK,CAAC,CAAA,SAAU,MAAM,IAAI,KAAK,OAAO,IAAI,CAAC,QAAQ,MAAM;AAC1E;AACA,SAAS,SAAS,OAAO,EAAE,GAAG;IAC5B,OAAO,OAAO,YAAY,aAAa,QAAQ,OAAO;AACxD;AAGO,SAAS;IACd,SAAS,cAAc,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM;QAC7C,MAAM,QAAQ;YACZ,CAAC,KAAK,EAAE;YACR;QACF;QACA,MAAM,UAAU,MAAM,CAAC,KAAK;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;gBACL,CAAC,KAAK,EAAE;YACV;QACF;QACA,MAAM,EACJ,cAAc,IAAI,EAClB,QAAQ,EACR,SAAS,EACT,KAAK,EACN,GAAG;QACJ,IAAI,OAAO,MAAM;YACf,OAAO;QACT;QAEA,qEAAqE;QACrE,IAAI,aAAa,gBAAgB,QAAQ,WAAW;YAClD,OAAO;gBACL,CAAC,KAAK,EAAE;YACV;QACF;QACA,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,OAAO,aAAa,CAAC;QAClD,IAAI,OAAO;YACT,OAAO,MAAM;QACf;QACA,MAAM,qBAAqB,CAAA;YACzB,IAAI,QAAQ,CAAA,GAAA,wJAAA,CAAA,gBAAQ,AAAD,EAAE,cAAc,WAAW;YAC9C,IAAI,mBAAmB,SAAS,OAAO,mBAAmB,UAAU;gBAClE,sBAAsB;gBACtB,QAAQ,CAAA,GAAA,wJAAA,CAAA,gBAAQ,AAAD,EAAE,cAAc,WAAW,GAAG,OAAO,mBAAmB,YAAY,KAAK,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD,EAAE,iBAAiB,EAAE;YACxH;YACA,IAAI,gBAAgB,OAAO;gBACzB,OAAO;YACT;YACA,OAAO;gBACL,CAAC,YAAY,EAAE;YACjB;QACF;QACA,OAAO,CAAA,GAAA,oKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,KAAK;IACvC;IACA,SAAS,gBAAgB,KAAK;QAC5B,MAAM,EACJ,EAAE,EACF,QAAQ,CAAC,CAAC,EACX,GAAG,SAAS,CAAC;QACd,IAAI,CAAC,IAAI;YACP,OAAO,MAAM,gDAAgD;QAC/D;QACA,MAAM,SAAS,MAAM,iBAAiB,IAAI,4KAAA,CAAA,UAAe;QAEzD;;;;KAIC,GACD,SAAS,SAAS,OAAO;YACvB,IAAI,WAAW;YACf,IAAI,OAAO,YAAY,YAAY;gBACjC,WAAW,QAAQ;YACrB,OAAO,IAAI,OAAO,YAAY,UAAU;gBACtC,QAAQ;gBACR,OAAO;YACT;YACA,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YACA,MAAM,mBAAmB,CAAA,GAAA,oKAAA,CAAA,8BAA2B,AAAD,EAAE,MAAM,WAAW;YACtE,MAAM,kBAAkB,OAAO,IAAI,CAAC;YACpC,IAAI,MAAM;YACV,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,CAAA;gBAC5B,MAAM,QAAQ,SAAS,QAAQ,CAAC,SAAS,EAAE;gBAC3C,IAAI,UAAU,QAAQ,UAAU,WAAW;oBACzC,IAAI,OAAO,UAAU,UAAU;wBAC7B,IAAI,MAAM,CAAC,SAAS,EAAE;4BACpB,MAAM,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE,KAAK,cAAc,UAAU,OAAO,OAAO;wBACzD,OAAO;4BACL,MAAM,oBAAoB,CAAA,GAAA,oKAAA,CAAA,oBAAiB,AAAD,EAAE;gCAC1C;4BACF,GAAG,OAAO,CAAA,IAAK,CAAC;oCACd,CAAC,SAAS,EAAE;gCACd,CAAC;4BACD,IAAI,oBAAoB,mBAAmB,QAAQ;gCACjD,GAAG,CAAC,SAAS,GAAG,gBAAgB;oCAC9B,IAAI;oCACJ;gCACF;4BACF,OAAO;gCACL,MAAM,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE,KAAK;4BACnB;wBACF;oBACF,OAAO;wBACL,MAAM,CAAA,GAAA,wJAAA,CAAA,UAAK,AAAD,EAAE,KAAK,cAAc,UAAU,OAAO,OAAO;oBACzD;gBACF;YACF;YACA,OAAO,CAAA,GAAA,oLAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,CAAA,GAAA,oKAAA,CAAA,0BAAuB,AAAD,EAAE,iBAAiB;QAC9E;QACA,OAAO,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,YAAY,SAAS;IACzD;IACA,OAAO;AACT;AACA,MAAM,kBAAkB;AACxB,gBAAgB,WAAW,GAAG;IAAC;CAAK;uCACrB", "ignoreList": [0]}}, {"offset": {"line": 1665, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/createTheme/createTheme.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nimport createBreakpoints from \"../createBreakpoints/createBreakpoints.js\";\nimport cssContainerQueries from \"../cssContainerQueries/index.js\";\nimport shape from \"./shape.js\";\nimport createSpacing from \"./createSpacing.js\";\nimport styleFunctionSx from \"../styleFunctionSx/styleFunctionSx.js\";\nimport defaultSxConfig from \"../styleFunctionSx/defaultSxConfig.js\";\nimport applyStyles from \"./applyStyles.js\";\nfunction createTheme(options = {}, ...args) {\n  const {\n    breakpoints: breakpointsInput = {},\n    palette: paletteInput = {},\n    spacing: spacingInput,\n    shape: shapeInput = {},\n    ...other\n  } = options;\n  const breakpoints = createBreakpoints(breakpointsInput);\n  const spacing = createSpacing(spacingInput);\n  let muiTheme = deepmerge({\n    breakpoints,\n    direction: 'ltr',\n    components: {},\n    // Inject component definitions.\n    palette: {\n      mode: 'light',\n      ...paletteInput\n    },\n    spacing,\n    shape: {\n      ...shape,\n      ...shapeInput\n    }\n  }, other);\n  muiTheme = cssContainerQueries(muiTheme);\n  muiTheme.applyStyles = applyStyles;\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nexport default createTheme;"], "names": [], "mappings": ";;;AACA;AAGA;AAJA;AAGA;AADA;AAKA;AADA;AADA;;;;;;;;;AAGA,SAAS,YAAY,UAAU,CAAC,CAAC,EAAE,GAAG,IAAI;IACxC,MAAM,EACJ,aAAa,mBAAmB,CAAC,CAAC,EAClC,SAAS,eAAe,CAAC,CAAC,EAC1B,SAAS,YAAY,EACrB,OAAO,aAAa,CAAC,CAAC,EACtB,GAAG,OACJ,GAAG;IACJ,MAAM,cAAc,CAAA,GAAA,gLAAA,CAAA,UAAiB,AAAD,EAAE;IACtC,MAAM,UAAU,CAAA,GAAA,sKAAA,CAAA,UAAa,AAAD,EAAE;IAC9B,IAAI,WAAW,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE;QACvB;QACA,WAAW;QACX,YAAY,CAAC;QACb,gCAAgC;QAChC,SAAS;YACP,MAAM;YACN,GAAG,YAAY;QACjB;QACA;QACA,OAAO;YACL,GAAG,8JAAA,CAAA,UAAK;YACR,GAAG,UAAU;QACf;IACF,GAAG;IACH,WAAW,CAAA,GAAA,oLAAA,CAAA,UAAmB,AAAD,EAAE;IAC/B,SAAS,WAAW,GAAG,oKAAA,CAAA,UAAW;IAClC,WAAW,KAAK,MAAM,CAAC,CAAC,KAAK,WAAa,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,KAAK,WAAW;IACpE,SAAS,iBAAiB,GAAG;QAC3B,GAAG,4KAAA,CAAA,UAAe;QAClB,GAAG,OAAO,iBAAiB;IAC7B;IACA,SAAS,WAAW,GAAG,SAAS,GAAG,KAAK;QACtC,OAAO,CAAA,GAAA,4KAAA,CAAA,UAAe,AAAD,EAAE;YACrB,IAAI;YACJ,OAAO,IAAI;QACb;IACF;IACA,OAAO;AACT;uCACe", "ignoreList": [0]}}, {"offset": {"line": 1725, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1731, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/preprocessStyles.js"], "sourcesContent": ["import { internal_serializeStyles } from '@mui/styled-engine';\nexport default function preprocessStyles(input) {\n  const {\n    variants,\n    ...style\n  } = input;\n  const result = {\n    variants,\n    style: internal_serializeStyles(style),\n    isProcessed: true\n  };\n\n  // Not supported on styled-components\n  if (result.style === style) {\n    return result;\n  }\n  if (variants) {\n    variants.forEach(variant => {\n      if (typeof variant.style !== 'function') {\n        variant.style = internal_serializeStyles(variant.style);\n      }\n    });\n  }\n  return result;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,iBAAiB,KAAK;IAC5C,MAAM,EACJ,QAAQ,EACR,GAAG,OACJ,GAAG;IACJ,MAAM,SAAS;QACb;QACA,OAAO,CAAA,GAAA,kKAAA,CAAA,2BAAwB,AAAD,EAAE;QAChC,aAAa;IACf;IAEA,qCAAqC;IACrC,IAAI,OAAO,KAAK,KAAK,OAAO;QAC1B,OAAO;IACT;IACA,IAAI,UAAU;QACZ,SAAS,OAAO,CAAC,CAAA;YACf,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;gBACvC,QAAQ,KAAK,GAAG,CAAA,GAAA,kKAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ,KAAK;YACxD;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 1756, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1762, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/createStyled/createStyled.js"], "sourcesContent": ["import styledEngineStyled, { internal_mutateStyles as mutateStyles } from '@mui/styled-engine';\nimport { isPlainObject } from '@mui/utils/deepmerge';\nimport capitalize from '@mui/utils/capitalize';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport createTheme from \"../createTheme/index.js\";\nimport styleFunctionSx from \"../styleFunctionSx/index.js\";\nimport preprocessStyles from \"../preprocessStyles.js\";\n\n/* eslint-disable no-underscore-dangle */\n/* eslint-disable no-labels */\n/* eslint-disable no-lone-blocks */\n\nexport const systemDefaultTheme = createTheme();\n\n// Update /system/styled/#api in case if this changes\nexport function shouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nfunction defaultOverridesResolver(slot) {\n  if (!slot) {\n    return null;\n  }\n  return (_props, styles) => styles[slot];\n}\nfunction attachTheme(props, themeId, defaultTheme) {\n  props.theme = isObjectEmpty(props.theme) ? defaultTheme : props.theme[themeId] || props.theme;\n}\nfunction processStyle(props, style) {\n  /*\n   * Style types:\n   *  - null/undefined\n   *  - string\n   *  - CSS style object: { [cssKey]: [cssValue], variants }\n   *  - Processed style object: { style, variants, isProcessed: true }\n   *  - Array of any of the above\n   */\n\n  const resolvedStyle = typeof style === 'function' ? style(props) : style;\n  if (Array.isArray(resolvedStyle)) {\n    return resolvedStyle.flatMap(subStyle => processStyle(props, subStyle));\n  }\n  if (Array.isArray(resolvedStyle?.variants)) {\n    let rootStyle;\n    if (resolvedStyle.isProcessed) {\n      rootStyle = resolvedStyle.style;\n    } else {\n      const {\n        variants,\n        ...otherStyles\n      } = resolvedStyle;\n      rootStyle = otherStyles;\n    }\n    return processStyleVariants(props, resolvedStyle.variants, [rootStyle]);\n  }\n  if (resolvedStyle?.isProcessed) {\n    return resolvedStyle.style;\n  }\n  return resolvedStyle;\n}\nfunction processStyleVariants(props, variants, results = []) {\n  let mergedState; // We might not need it, initialized lazily\n\n  variantLoop: for (let i = 0; i < variants.length; i += 1) {\n    const variant = variants[i];\n    if (typeof variant.props === 'function') {\n      mergedState ??= {\n        ...props,\n        ...props.ownerState,\n        ownerState: props.ownerState\n      };\n      if (!variant.props(mergedState)) {\n        continue;\n      }\n    } else {\n      for (const key in variant.props) {\n        if (props[key] !== variant.props[key] && props.ownerState?.[key] !== variant.props[key]) {\n          continue variantLoop;\n        }\n      }\n    }\n    if (typeof variant.style === 'function') {\n      mergedState ??= {\n        ...props,\n        ...props.ownerState,\n        ownerState: props.ownerState\n      };\n      results.push(variant.style(mergedState));\n    } else {\n      results.push(variant.style);\n    }\n  }\n  return results;\n}\nexport default function createStyled(input = {}) {\n  const {\n    themeId,\n    defaultTheme = systemDefaultTheme,\n    rootShouldForwardProp = shouldForwardProp,\n    slotShouldForwardProp = shouldForwardProp\n  } = input;\n  function styleAttachTheme(props) {\n    attachTheme(props, themeId, defaultTheme);\n  }\n  const styled = (tag, inputOptions = {}) => {\n    // If `tag` is already a styled component, filter out the `sx` style function\n    // to prevent unnecessary styles generated by the composite components.\n    mutateStyles(tag, styles => styles.filter(style => style !== styleFunctionSx));\n    const {\n      name: componentName,\n      slot: componentSlot,\n      skipVariantsResolver: inputSkipVariantsResolver,\n      skipSx: inputSkipSx,\n      // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n      // For more details: https://github.com/mui/material-ui/pull/37908\n      overridesResolver = defaultOverridesResolver(lowercaseFirstLetter(componentSlot)),\n      ...options\n    } = inputOptions;\n\n    // if skipVariantsResolver option is defined, take the value, otherwise, true for root and false for other slots.\n    const skipVariantsResolver = inputSkipVariantsResolver !== undefined ? inputSkipVariantsResolver :\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    componentSlot && componentSlot !== 'Root' && componentSlot !== 'root' || false;\n    const skipSx = inputSkipSx || false;\n    let shouldForwardPropOption = shouldForwardProp;\n\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    if (componentSlot === 'Root' || componentSlot === 'root') {\n      shouldForwardPropOption = rootShouldForwardProp;\n    } else if (componentSlot) {\n      // any other slot specified\n      shouldForwardPropOption = slotShouldForwardProp;\n    } else if (isStringTag(tag)) {\n      // for string (html) tag, preserve the behavior in emotion & styled-components.\n      shouldForwardPropOption = undefined;\n    }\n    const defaultStyledResolver = styledEngineStyled(tag, {\n      shouldForwardProp: shouldForwardPropOption,\n      label: generateStyledLabel(componentName, componentSlot),\n      ...options\n    });\n    const transformStyle = style => {\n      // On the server Emotion doesn't use React.forwardRef for creating components, so the created\n      // component stays as a function. This condition makes sure that we do not interpolate functions\n      // which are basically components used as a selectors.\n      if (typeof style === 'function' && style.__emotion_real !== style) {\n        return function styleFunctionProcessor(props) {\n          return processStyle(props, style);\n        };\n      }\n      if (isPlainObject(style)) {\n        const serialized = preprocessStyles(style);\n        if (!serialized.variants) {\n          return serialized.style;\n        }\n        return function styleObjectProcessor(props) {\n          return processStyle(props, serialized);\n        };\n      }\n      return style;\n    };\n    const muiStyledResolver = (...expressionsInput) => {\n      const expressionsHead = [];\n      const expressionsBody = expressionsInput.map(transformStyle);\n      const expressionsTail = [];\n\n      // Preprocess `props` to set the scoped theme value.\n      // This must run before any other expression.\n      expressionsHead.push(styleAttachTheme);\n      if (componentName && overridesResolver) {\n        expressionsTail.push(function styleThemeOverrides(props) {\n          const theme = props.theme;\n          const styleOverrides = theme.components?.[componentName]?.styleOverrides;\n          if (!styleOverrides) {\n            return null;\n          }\n          const resolvedStyleOverrides = {};\n\n          // TODO: v7 remove iteration and use `resolveStyleArg(styleOverrides[slot])` directly\n          // eslint-disable-next-line guard-for-in\n          for (const slotKey in styleOverrides) {\n            resolvedStyleOverrides[slotKey] = processStyle(props, styleOverrides[slotKey]);\n          }\n          return overridesResolver(props, resolvedStyleOverrides);\n        });\n      }\n      if (componentName && !skipVariantsResolver) {\n        expressionsTail.push(function styleThemeVariants(props) {\n          const theme = props.theme;\n          const themeVariants = theme?.components?.[componentName]?.variants;\n          if (!themeVariants) {\n            return null;\n          }\n          return processStyleVariants(props, themeVariants);\n        });\n      }\n      if (!skipSx) {\n        expressionsTail.push(styleFunctionSx);\n      }\n\n      // This function can be called as a tagged template, so the first argument would contain\n      // CSS `string[]` values.\n      if (Array.isArray(expressionsBody[0])) {\n        const inputStrings = expressionsBody.shift();\n\n        // We need to add placeholders in the tagged template for the custom functions we have\n        // possibly added (attachTheme, overrides, variants, and sx).\n        const placeholdersHead = new Array(expressionsHead.length).fill('');\n        const placeholdersTail = new Array(expressionsTail.length).fill('');\n        let outputStrings;\n        // prettier-ignore\n        {\n          outputStrings = [...placeholdersHead, ...inputStrings, ...placeholdersTail];\n          outputStrings.raw = [...placeholdersHead, ...inputStrings.raw, ...placeholdersTail];\n        }\n\n        // The only case where we put something before `attachTheme`\n        expressionsHead.unshift(outputStrings);\n      }\n      const expressions = [...expressionsHead, ...expressionsBody, ...expressionsTail];\n      const Component = defaultStyledResolver(...expressions);\n      if (tag.muiName) {\n        Component.muiName = tag.muiName;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        Component.displayName = generateDisplayName(componentName, componentSlot, tag);\n      }\n      return Component;\n    };\n    if (defaultStyledResolver.withConfig) {\n      muiStyledResolver.withConfig = defaultStyledResolver.withConfig;\n    }\n    return muiStyledResolver;\n  };\n  return styled;\n}\nfunction generateDisplayName(componentName, componentSlot, tag) {\n  if (componentName) {\n    return `${componentName}${capitalize(componentSlot || '')}`;\n  }\n  return `Styled(${getDisplayName(tag)})`;\n}\nfunction generateStyledLabel(componentName, componentSlot) {\n  let label;\n  if (process.env.NODE_ENV !== 'production') {\n    if (componentName) {\n      // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n      // For more details: https://github.com/mui/material-ui/pull/37908\n      label = `${componentName}-${lowercaseFirstLetter(componentSlot || 'Root')}`;\n    }\n  }\n  return label;\n}\nfunction isObjectEmpty(object) {\n  // eslint-disable-next-line\n  for (const _ in object) {\n    return false;\n  }\n  return true;\n}\n\n// https://github.com/emotion-js/emotion/blob/26ded6109fcd8ca9875cc2ce4564fee678a3f3c5/packages/styled/src/utils.js#L40\nfunction isStringTag(tag) {\n  return typeof tag === 'string' &&\n  // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96;\n}\nfunction lowercaseFirstLetter(string) {\n  if (!string) {\n    return string;\n  }\n  return string.charAt(0).toLowerCase() + string.slice(1);\n}"], "names": [], "mappings": ";;;;;AAIA;AAJA;AAKA;AAJA;AAKA;AAHA;AADA;;;;;;;;AAUO,MAAM,qBAAqB,CAAA,GAAA,oKAAA,CAAA,UAAW,AAAD;AAGrC,SAAS,kBAAkB,IAAI;IACpC,OAAO,SAAS,gBAAgB,SAAS,WAAW,SAAS,QAAQ,SAAS;AAChF;AACA,SAAS,yBAAyB,IAAI;IACpC,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,OAAO,CAAC,QAAQ,SAAW,MAAM,CAAC,KAAK;AACzC;AACA,SAAS,YAAY,KAAK,EAAE,OAAO,EAAE,YAAY;IAC/C,MAAM,KAAK,GAAG,cAAc,MAAM,KAAK,IAAI,eAAe,MAAM,KAAK,CAAC,QAAQ,IAAI,MAAM,KAAK;AAC/F;AACA,SAAS,aAAa,KAAK,EAAE,KAAK;IAChC;;;;;;;GAOC,GAED,MAAM,gBAAgB,OAAO,UAAU,aAAa,MAAM,SAAS;IACnE,IAAI,MAAM,OAAO,CAAC,gBAAgB;QAChC,OAAO,cAAc,OAAO,CAAC,CAAA,WAAY,aAAa,OAAO;IAC/D;IACA,IAAI,MAAM,OAAO,CAAC,eAAe,WAAW;QAC1C,IAAI;QACJ,IAAI,cAAc,WAAW,EAAE;YAC7B,YAAY,cAAc,KAAK;QACjC,OAAO;YACL,MAAM,EACJ,QAAQ,EACR,GAAG,aACJ,GAAG;YACJ,YAAY;QACd;QACA,OAAO,qBAAqB,OAAO,cAAc,QAAQ,EAAE;YAAC;SAAU;IACxE;IACA,IAAI,eAAe,aAAa;QAC9B,OAAO,cAAc,KAAK;IAC5B;IACA,OAAO;AACT;AACA,SAAS,qBAAqB,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;IACzD,IAAI,aAAa,2CAA2C;IAE5D,aAAa,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;QACxD,MAAM,UAAU,QAAQ,CAAC,EAAE;QAC3B,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;YACvC,gBAAgB;gBACd,GAAG,KAAK;gBACR,GAAG,MAAM,UAAU;gBACnB,YAAY,MAAM,UAAU;YAC9B;YACA,IAAI,CAAC,QAAQ,KAAK,CAAC,cAAc;gBAC/B;YACF;QACF,OAAO;YACL,IAAK,MAAM,OAAO,QAAQ,KAAK,CAAE;gBAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,KAAK,CAAC,IAAI,IAAI,MAAM,UAAU,EAAE,CAAC,IAAI,KAAK,QAAQ,KAAK,CAAC,IAAI,EAAE;oBACvF,SAAS;gBACX;YACF;QACF;QACA,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;YACvC,gBAAgB;gBACd,GAAG,KAAK;gBACR,GAAG,MAAM,UAAU;gBACnB,YAAY,MAAM,UAAU;YAC9B;YACA,QAAQ,IAAI,CAAC,QAAQ,KAAK,CAAC;QAC7B,OAAO;YACL,QAAQ,IAAI,CAAC,QAAQ,KAAK;QAC5B;IACF;IACA,OAAO;AACT;AACe,SAAS,aAAa,QAAQ,CAAC,CAAC;IAC7C,MAAM,EACJ,OAAO,EACP,eAAe,kBAAkB,EACjC,wBAAwB,iBAAiB,EACzC,wBAAwB,iBAAiB,EAC1C,GAAG;IACJ,SAAS,iBAAiB,KAAK;QAC7B,YAAY,OAAO,SAAS;IAC9B;IACA,MAAM,SAAS,CAAC,KAAK,eAAe,CAAC,CAAC;QACpC,6EAA6E;QAC7E,uEAAuE;QACvE,CAAA,GAAA,kKAAA,CAAA,wBAAY,AAAD,EAAE,KAAK,CAAA,SAAU,OAAO,MAAM,CAAC,CAAA,QAAS,UAAU,4KAAA,CAAA,UAAe;QAC5E,MAAM,EACJ,MAAM,aAAa,EACnB,MAAM,aAAa,EACnB,sBAAsB,yBAAyB,EAC/C,QAAQ,WAAW,EACnB,qEAAqE;QACrE,kEAAkE;QAClE,oBAAoB,yBAAyB,qBAAqB,eAAe,EACjF,GAAG,SACJ,GAAG;QAEJ,iHAAiH;QACjH,MAAM,uBAAuB,8BAA8B,YAAY,4BACvE,mDAAmD;QACnD,kEAAkE;QAClE,iBAAiB,kBAAkB,UAAU,kBAAkB,UAAU;QACzE,MAAM,SAAS,eAAe;QAC9B,IAAI,0BAA0B;QAE9B,mDAAmD;QACnD,kEAAkE;QAClE,IAAI,kBAAkB,UAAU,kBAAkB,QAAQ;YACxD,0BAA0B;QAC5B,OAAO,IAAI,eAAe;YACxB,2BAA2B;YAC3B,0BAA0B;QAC5B,OAAO,IAAI,YAAY,MAAM;YAC3B,+EAA+E;YAC/E,0BAA0B;QAC5B;QACA,MAAM,wBAAwB,CAAA,GAAA,kKAAA,CAAA,UAAkB,AAAD,EAAE,KAAK;YACpD,mBAAmB;YACnB,OAAO,oBAAoB,eAAe;YAC1C,GAAG,OAAO;QACZ;QACA,MAAM,iBAAiB,CAAA;YACrB,6FAA6F;YAC7F,gGAAgG;YAChG,sDAAsD;YACtD,IAAI,OAAO,UAAU,cAAc,MAAM,cAAc,KAAK,OAAO;gBACjE,OAAO,SAAS,uBAAuB,KAAK;oBAC1C,OAAO,aAAa,OAAO;gBAC7B;YACF;YACA,IAAI,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBACxB,MAAM,aAAa,CAAA,GAAA,0JAAA,CAAA,UAAgB,AAAD,EAAE;gBACpC,IAAI,CAAC,WAAW,QAAQ,EAAE;oBACxB,OAAO,WAAW,KAAK;gBACzB;gBACA,OAAO,SAAS,qBAAqB,KAAK;oBACxC,OAAO,aAAa,OAAO;gBAC7B;YACF;YACA,OAAO;QACT;QACA,MAAM,oBAAoB,CAAC,GAAG;YAC5B,MAAM,kBAAkB,EAAE;YAC1B,MAAM,kBAAkB,iBAAiB,GAAG,CAAC;YAC7C,MAAM,kBAAkB,EAAE;YAE1B,oDAAoD;YACpD,6CAA6C;YAC7C,gBAAgB,IAAI,CAAC;YACrB,IAAI,iBAAiB,mBAAmB;gBACtC,gBAAgB,IAAI,CAAC,SAAS,oBAAoB,KAAK;oBACrD,MAAM,QAAQ,MAAM,KAAK;oBACzB,MAAM,iBAAiB,MAAM,UAAU,EAAE,CAAC,cAAc,EAAE;oBAC1D,IAAI,CAAC,gBAAgB;wBACnB,OAAO;oBACT;oBACA,MAAM,yBAAyB,CAAC;oBAEhC,qFAAqF;oBACrF,wCAAwC;oBACxC,IAAK,MAAM,WAAW,eAAgB;wBACpC,sBAAsB,CAAC,QAAQ,GAAG,aAAa,OAAO,cAAc,CAAC,QAAQ;oBAC/E;oBACA,OAAO,kBAAkB,OAAO;gBAClC;YACF;YACA,IAAI,iBAAiB,CAAC,sBAAsB;gBAC1C,gBAAgB,IAAI,CAAC,SAAS,mBAAmB,KAAK;oBACpD,MAAM,QAAQ,MAAM,KAAK;oBACzB,MAAM,gBAAgB,OAAO,YAAY,CAAC,cAAc,EAAE;oBAC1D,IAAI,CAAC,eAAe;wBAClB,OAAO;oBACT;oBACA,OAAO,qBAAqB,OAAO;gBACrC;YACF;YACA,IAAI,CAAC,QAAQ;gBACX,gBAAgB,IAAI,CAAC,4KAAA,CAAA,UAAe;YACtC;YAEA,wFAAwF;YACxF,yBAAyB;YACzB,IAAI,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,GAAG;gBACrC,MAAM,eAAe,gBAAgB,KAAK;gBAE1C,sFAAsF;gBACtF,6DAA6D;gBAC7D,MAAM,mBAAmB,IAAI,MAAM,gBAAgB,MAAM,EAAE,IAAI,CAAC;gBAChE,MAAM,mBAAmB,IAAI,MAAM,gBAAgB,MAAM,EAAE,IAAI,CAAC;gBAChE,IAAI;gBACJ,kBAAkB;gBAClB;oBACE,gBAAgB;2BAAI;2BAAqB;2BAAiB;qBAAiB;oBAC3E,cAAc,GAAG,GAAG;2BAAI;2BAAqB,aAAa,GAAG;2BAAK;qBAAiB;gBACrF;gBAEA,4DAA4D;gBAC5D,gBAAgB,OAAO,CAAC;YAC1B;YACA,MAAM,cAAc;mBAAI;mBAAoB;mBAAoB;aAAgB;YAChF,MAAM,YAAY,yBAAyB;YAC3C,IAAI,IAAI,OAAO,EAAE;gBACf,UAAU,OAAO,GAAG,IAAI,OAAO;YACjC;YACA,wCAA2C;gBACzC,UAAU,WAAW,GAAG,oBAAoB,eAAe,eAAe;YAC5E;YACA,OAAO;QACT;QACA,IAAI,sBAAsB,UAAU,EAAE;YACpC,kBAAkB,UAAU,GAAG,sBAAsB,UAAU;QACjE;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,aAAa,EAAE,aAAa,EAAE,GAAG;IAC5D,IAAI,eAAe;QACjB,OAAO,GAAG,gBAAgB,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD,EAAE,iBAAiB,KAAK;IAC7D;IACA,OAAO,CAAC,OAAO,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,KAAK,CAAC,CAAC;AACzC;AACA,SAAS,oBAAoB,aAAa,EAAE,aAAa;IACvD,IAAI;IACJ,wCAA2C;QACzC,IAAI,eAAe;YACjB,qEAAqE;YACrE,kEAAkE;YAClE,QAAQ,GAAG,cAAc,CAAC,EAAE,qBAAqB,iBAAiB,SAAS;QAC7E;IACF;IACA,OAAO;AACT;AACA,SAAS,cAAc,MAAM;IAC3B,2BAA2B;IAC3B,IAAK,MAAM,KAAK,OAAQ;QACtB,OAAO;IACT;IACA,OAAO;AACT;AAEA,uHAAuH;AACvH,SAAS,YAAY,GAAG;IACtB,OAAO,OAAO,QAAQ,YACtB,oCAAoC;IACpC,mCAAmC;IACnC,6BAA6B;IAC7B,IAAI,UAAU,CAAC,KAAK;AACtB;AACA,SAAS,qBAAqB,MAAM;IAClC,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;AACvD", "ignoreList": [0]}}, {"offset": {"line": 2029, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2035, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/cssVars/prepareTypographyVars.js"], "sourcesContent": ["export default function prepareTypographyVars(typography) {\n  const vars = {};\n  const entries = Object.entries(typography);\n  entries.forEach(entry => {\n    const [key, value] = entry;\n    if (typeof value === 'object') {\n      vars[key] = `${value.fontStyle ? `${value.fontStyle} ` : ''}${value.fontVariant ? `${value.fontVariant} ` : ''}${value.fontWeight ? `${value.fontWeight} ` : ''}${value.fontStretch ? `${value.fontStretch} ` : ''}${value.fontSize || ''}${value.lineHeight ? `/${value.lineHeight} ` : ''}${value.fontFamily || ''}`;\n    }\n  });\n  return vars;\n}"], "names": [], "mappings": ";;;AAAe,SAAS,sBAAsB,UAAU;IACtD,MAAM,OAAO,CAAC;IACd,MAAM,UAAU,OAAO,OAAO,CAAC;IAC/B,QAAQ,OAAO,CAAC,CAAA;QACd,MAAM,CAAC,KAAK,MAAM,GAAG;QACrB,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,IAAI,GAAG,GAAG,MAAM,SAAS,GAAG,GAAG,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,MAAM,WAAW,GAAG,GAAG,MAAM,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,MAAM,UAAU,GAAG,GAAG,MAAM,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,MAAM,WAAW,GAAG,GAAG,MAAM,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,MAAM,QAAQ,IAAI,KAAK,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,MAAM,UAAU,IAAI,IAAI;QACxT;IACF;IACA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 2049, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2065, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/colorManipulator/colorManipulator.js"], "sourcesContent": ["import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n/* eslint-disable @typescript-eslint/naming-convention */\nimport clamp from '@mui/utils/clamp';\n\n/**\n * Returns a number whose value is limited to the given range.\n * @param {number} value The value to be clamped\n * @param {number} min The lower boundary of the output range\n * @param {number} max The upper boundary of the output range\n * @returns {number} A number in the range [min, max]\n */\nfunction clampWrapper(value, min = 0, max = 1) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (value < min || value > max) {\n      console.error(`MUI: The value provided ${value} is out of range [${min}, ${max}].`);\n    }\n  }\n  return clamp(value, min, max);\n}\n\n/**\n * Converts a color from CSS hex format to CSS rgb format.\n * @param {string} color - Hex color, i.e. #nnn or #nnnnnn\n * @returns {string} A CSS rgb color string\n */\nexport function hexToRgb(color) {\n  color = color.slice(1);\n  const re = new RegExp(`.{1,${color.length >= 6 ? 2 : 1}}`, 'g');\n  let colors = color.match(re);\n  if (colors && colors[0].length === 1) {\n    colors = colors.map(n => n + n);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (color.length !== color.trim().length) {\n      console.error(`MUI: The color: \"${color}\" is invalid. Make sure the color input doesn't contain leading/trailing space.`);\n    }\n  }\n  return colors ? `rgb${colors.length === 4 ? 'a' : ''}(${colors.map((n, index) => {\n    return index < 3 ? parseInt(n, 16) : Math.round(parseInt(n, 16) / 255 * 1000) / 1000;\n  }).join(', ')})` : '';\n}\nfunction intToHex(int) {\n  const hex = int.toString(16);\n  return hex.length === 1 ? `0${hex}` : hex;\n}\n\n/**\n * Returns an object with the type and values of a color.\n *\n * Note: Does not support rgb % values.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {object} - A MUI color object: {type: string, values: number[]}\n */\nexport function decomposeColor(color) {\n  // Idempotent\n  if (color.type) {\n    return color;\n  }\n  if (color.charAt(0) === '#') {\n    return decomposeColor(hexToRgb(color));\n  }\n  const marker = color.indexOf('(');\n  const type = color.substring(0, marker);\n  if (!['rgb', 'rgba', 'hsl', 'hsla', 'color'].includes(type)) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: Unsupported \\`${color}\\` color.\\n` + 'The following formats are supported: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().' : _formatMuiErrorMessage(9, color));\n  }\n  let values = color.substring(marker + 1, color.length - 1);\n  let colorSpace;\n  if (type === 'color') {\n    values = values.split(' ');\n    colorSpace = values.shift();\n    if (values.length === 4 && values[3].charAt(0) === '/') {\n      values[3] = values[3].slice(1);\n    }\n    if (!['srgb', 'display-p3', 'a98-rgb', 'prophoto-rgb', 'rec-2020'].includes(colorSpace)) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: unsupported \\`${colorSpace}\\` color space.\\n` + 'The following color spaces are supported: srgb, display-p3, a98-rgb, prophoto-rgb, rec-2020.' : _formatMuiErrorMessage(10, colorSpace));\n    }\n  } else {\n    values = values.split(',');\n  }\n  values = values.map(value => parseFloat(value));\n  return {\n    type,\n    values,\n    colorSpace\n  };\n}\n\n/**\n * Returns a channel created from the input color.\n *\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {string} - The channel for the color, that can be used in rgba or hsla colors\n */\nexport const colorChannel = color => {\n  const decomposedColor = decomposeColor(color);\n  return decomposedColor.values.slice(0, 3).map((val, idx) => decomposedColor.type.includes('hsl') && idx !== 0 ? `${val}%` : val).join(' ');\n};\nexport const private_safeColorChannel = (color, warning) => {\n  try {\n    return colorChannel(color);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n};\n\n/**\n * Converts a color object with type and values to a string.\n * @param {object} color - Decomposed color\n * @param {string} color.type - One of: 'rgb', 'rgba', 'hsl', 'hsla', 'color'\n * @param {array} color.values - [n,n,n] or [n,n,n,n]\n * @returns {string} A CSS color string\n */\nexport function recomposeColor(color) {\n  const {\n    type,\n    colorSpace\n  } = color;\n  let {\n    values\n  } = color;\n  if (type.includes('rgb')) {\n    // Only convert the first 3 values to int (i.e. not alpha)\n    values = values.map((n, i) => i < 3 ? parseInt(n, 10) : n);\n  } else if (type.includes('hsl')) {\n    values[1] = `${values[1]}%`;\n    values[2] = `${values[2]}%`;\n  }\n  if (type.includes('color')) {\n    values = `${colorSpace} ${values.join(' ')}`;\n  } else {\n    values = `${values.join(', ')}`;\n  }\n  return `${type}(${values})`;\n}\n\n/**\n * Converts a color from CSS rgb format to CSS hex format.\n * @param {string} color - RGB color, i.e. rgb(n, n, n)\n * @returns {string} A CSS rgb color string, i.e. #nnnnnn\n */\nexport function rgbToHex(color) {\n  // Idempotent\n  if (color.startsWith('#')) {\n    return color;\n  }\n  const {\n    values\n  } = decomposeColor(color);\n  return `#${values.map((n, i) => intToHex(i === 3 ? Math.round(255 * n) : n)).join('')}`;\n}\n\n/**\n * Converts a color from hsl format to rgb format.\n * @param {string} color - HSL color values\n * @returns {string} rgb color values\n */\nexport function hslToRgb(color) {\n  color = decomposeColor(color);\n  const {\n    values\n  } = color;\n  const h = values[0];\n  const s = values[1] / 100;\n  const l = values[2] / 100;\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  let type = 'rgb';\n  const rgb = [Math.round(f(0) * 255), Math.round(f(8) * 255), Math.round(f(4) * 255)];\n  if (color.type === 'hsla') {\n    type += 'a';\n    rgb.push(values[3]);\n  }\n  return recomposeColor({\n    type,\n    values: rgb\n  });\n}\n/**\n * The relative brightness of any point in a color space,\n * normalized to 0 for darkest black and 1 for lightest white.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {number} The relative brightness of the color in the range 0 - 1\n */\nexport function getLuminance(color) {\n  color = decomposeColor(color);\n  let rgb = color.type === 'hsl' || color.type === 'hsla' ? decomposeColor(hslToRgb(color)).values : color.values;\n  rgb = rgb.map(val => {\n    if (color.type !== 'color') {\n      val /= 255; // normalized\n    }\n    return val <= 0.03928 ? val / 12.92 : ((val + 0.055) / 1.055) ** 2.4;\n  });\n\n  // Truncate at 3 digits\n  return Number((0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2]).toFixed(3));\n}\n\n/**\n * Calculates the contrast ratio between two colors.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} foreground - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @param {string} background - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @returns {number} A contrast ratio value in the range 0 - 21.\n */\nexport function getContrastRatio(foreground, background) {\n  const lumA = getLuminance(foreground);\n  const lumB = getLuminance(background);\n  return (Math.max(lumA, lumB) + 0.05) / (Math.min(lumA, lumB) + 0.05);\n}\n\n/**\n * Sets the absolute transparency of a color.\n * Any existing alpha values are overwritten.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} value - value to set the alpha channel to in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function alpha(color, value) {\n  color = decomposeColor(color);\n  value = clampWrapper(value);\n  if (color.type === 'rgb' || color.type === 'hsl') {\n    color.type += 'a';\n  }\n  if (color.type === 'color') {\n    color.values[3] = `/${value}`;\n  } else {\n    color.values[3] = value;\n  }\n  return recomposeColor(color);\n}\nexport function private_safeAlpha(color, value, warning) {\n  try {\n    return alpha(color, value);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darkens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function darken(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.includes('hsl')) {\n    color.values[2] *= 1 - coefficient;\n  } else if (color.type.includes('rgb') || color.type.includes('color')) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] *= 1 - coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeDarken(color, coefficient, warning) {\n  try {\n    return darken(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Lightens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function lighten(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.includes('hsl')) {\n    color.values[2] += (100 - color.values[2]) * coefficient;\n  } else if (color.type.includes('rgb')) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (255 - color.values[i]) * coefficient;\n    }\n  } else if (color.type.includes('color')) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (1 - color.values[i]) * coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeLighten(color, coefficient, warning) {\n  try {\n    return lighten(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darken or lighten a color, depending on its luminance.\n * Light colors are darkened, dark colors are lightened.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient=0.15 - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function emphasize(color, coefficient = 0.15) {\n  return getLuminance(color) > 0.5 ? darken(color, coefficient) : lighten(color, coefficient);\n}\nexport function private_safeEmphasize(color, coefficient, warning) {\n  try {\n    return emphasize(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Blend a transparent overlay color with a background color, resulting in a single\n * RGB color.\n * @param {string} background - CSS color\n * @param {string} overlay - CSS color\n * @param {number} opacity - Opacity multiplier in the range 0 - 1\n * @param {number} [gamma=1.0] - Gamma correction factor. For gamma-correct blending, 2.2 is usual.\n */\nexport function blend(background, overlay, opacity, gamma = 1.0) {\n  const blendChannel = (b, o) => Math.round((b ** (1 / gamma) * (1 - opacity) + o ** (1 / gamma) * opacity) ** gamma);\n  const backgroundColor = decomposeColor(background);\n  const overlayColor = decomposeColor(overlay);\n  const rgb = [blendChannel(backgroundColor.values[0], overlayColor.values[0]), blendChannel(backgroundColor.values[1], overlayColor.values[1]), blendChannel(backgroundColor.values[2], overlayColor.values[2])];\n  return recomposeColor({\n    type: 'rgb',\n    values: rgb\n  });\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AACA,uDAAuD,GACvD;;;AAEA;;;;;;CAMC,GACD,SAAS,aAAa,KAAK,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;IAC3C,wCAA2C;QACzC,IAAI,QAAQ,OAAO,QAAQ,KAAK;YAC9B,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,MAAM,kBAAkB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;QACpF;IACF;IACA,OAAO,CAAA,GAAA,uJAAA,CAAA,UAAK,AAAD,EAAE,OAAO,KAAK;AAC3B;AAOO,SAAS,SAAS,KAAK;IAC5B,QAAQ,MAAM,KAAK,CAAC;IACpB,MAAM,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC,EAAE;IAC3D,IAAI,SAAS,MAAM,KAAK,CAAC;IACzB,IAAI,UAAU,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,GAAG;QACpC,SAAS,OAAO,GAAG,CAAC,CAAA,IAAK,IAAI;IAC/B;IACA,wCAA2C;QACzC,IAAI,MAAM,MAAM,KAAK,MAAM,IAAI,GAAG,MAAM,EAAE;YACxC,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,MAAM,+EAA+E,CAAC;QAC1H;IACF;IACA,OAAO,SAAS,CAAC,GAAG,EAAE,OAAO,MAAM,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG;QACrE,OAAO,QAAQ,IAAI,SAAS,GAAG,MAAM,KAAK,KAAK,CAAC,SAAS,GAAG,MAAM,MAAM,QAAQ;IAClF,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG;AACrB;AACA,SAAS,SAAS,GAAG;IACnB,MAAM,MAAM,IAAI,QAAQ,CAAC;IACzB,OAAO,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG;AACxC;AASO,SAAS,eAAe,KAAK;IAClC,aAAa;IACb,IAAI,MAAM,IAAI,EAAE;QACd,OAAO;IACT;IACA,IAAI,MAAM,MAAM,CAAC,OAAO,KAAK;QAC3B,OAAO,eAAe,SAAS;IACjC;IACA,MAAM,SAAS,MAAM,OAAO,CAAC;IAC7B,MAAM,OAAO,MAAM,SAAS,CAAC,GAAG;IAChC,IAAI,CAAC;QAAC;QAAO;QAAQ;QAAO;QAAQ;KAAQ,CAAC,QAAQ,CAAC,OAAO;QAC3D,MAAM,IAAI,MAAM,uCAAwC,CAAC,mBAAmB,EAAE,MAAM,WAAW,CAAC,GAAG;IACrG;IACA,IAAI,SAAS,MAAM,SAAS,CAAC,SAAS,GAAG,MAAM,MAAM,GAAG;IACxD,IAAI;IACJ,IAAI,SAAS,SAAS;QACpB,SAAS,OAAO,KAAK,CAAC;QACtB,aAAa,OAAO,KAAK;QACzB,IAAI,OAAO,MAAM,KAAK,KAAK,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,KAAK;YACtD,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;QAC9B;QACA,IAAI,CAAC;YAAC;YAAQ;YAAc;YAAW;YAAgB;SAAW,CAAC,QAAQ,CAAC,aAAa;YACvF,MAAM,IAAI,MAAM,uCAAwC,CAAC,mBAAmB,EAAE,WAAW,iBAAiB,CAAC,GAAG;QAChH;IACF,OAAO;QACL,SAAS,OAAO,KAAK,CAAC;IACxB;IACA,SAAS,OAAO,GAAG,CAAC,CAAA,QAAS,WAAW;IACxC,OAAO;QACL;QACA;QACA;IACF;AACF;AAQO,MAAM,eAAe,CAAA;IAC1B,MAAM,kBAAkB,eAAe;IACvC,OAAO,gBAAgB,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,MAAQ,gBAAgB,IAAI,CAAC,QAAQ,CAAC,UAAU,QAAQ,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC;AACxI;AACO,MAAM,2BAA2B,CAAC,OAAO;IAC9C,IAAI;QACF,OAAO,aAAa;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,WAAW,oDAAyB,cAAc;YACpD,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;AACF;AASO,SAAS,eAAe,KAAK;IAClC,MAAM,EACJ,IAAI,EACJ,UAAU,EACX,GAAG;IACJ,IAAI,EACF,MAAM,EACP,GAAG;IACJ,IAAI,KAAK,QAAQ,CAAC,QAAQ;QACxB,0DAA0D;QAC1D,SAAS,OAAO,GAAG,CAAC,CAAC,GAAG,IAAM,IAAI,IAAI,SAAS,GAAG,MAAM;IAC1D,OAAO,IAAI,KAAK,QAAQ,CAAC,QAAQ;QAC/B,MAAM,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,MAAM,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7B;IACA,IAAI,KAAK,QAAQ,CAAC,UAAU;QAC1B,SAAS,GAAG,WAAW,CAAC,EAAE,OAAO,IAAI,CAAC,MAAM;IAC9C,OAAO;QACL,SAAS,GAAG,OAAO,IAAI,CAAC,OAAO;IACjC;IACA,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;AAC7B;AAOO,SAAS,SAAS,KAAK;IAC5B,aAAa;IACb,IAAI,MAAM,UAAU,CAAC,MAAM;QACzB,OAAO;IACT;IACA,MAAM,EACJ,MAAM,EACP,GAAG,eAAe;IACnB,OAAO,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG,IAAM,SAAS,MAAM,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK;AACzF;AAOO,SAAS,SAAS,KAAK;IAC5B,QAAQ,eAAe;IACvB,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,MAAM,IAAI,MAAM,CAAC,EAAE;IACnB,MAAM,IAAI,MAAM,CAAC,EAAE,GAAG;IACtB,MAAM,IAAI,MAAM,CAAC,EAAE,GAAG;IACtB,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI;IAC9B,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,GAAK,IAAI,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IACrF,IAAI,OAAO;IACX,MAAM,MAAM;QAAC,KAAK,KAAK,CAAC,EAAE,KAAK;QAAM,KAAK,KAAK,CAAC,EAAE,KAAK;QAAM,KAAK,KAAK,CAAC,EAAE,KAAK;KAAK;IACpF,IAAI,MAAM,IAAI,KAAK,QAAQ;QACzB,QAAQ;QACR,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;IACpB;IACA,OAAO,eAAe;QACpB;QACA,QAAQ;IACV;AACF;AASO,SAAS,aAAa,KAAK;IAChC,QAAQ,eAAe;IACvB,IAAI,MAAM,MAAM,IAAI,KAAK,SAAS,MAAM,IAAI,KAAK,SAAS,eAAe,SAAS,QAAQ,MAAM,GAAG,MAAM,MAAM;IAC/G,MAAM,IAAI,GAAG,CAAC,CAAA;QACZ,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO,KAAK,aAAa;QAC3B;QACA,OAAO,OAAO,UAAU,MAAM,QAAQ,CAAC,CAAC,MAAM,KAAK,IAAI,KAAK,KAAK;IACnE;IAEA,uBAAuB;IACvB,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,GAAG,SAAS,GAAG,CAAC,EAAE,GAAG,SAAS,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC;AAC9E;AAUO,SAAS,iBAAiB,UAAU,EAAE,UAAU;IACrD,MAAM,OAAO,aAAa;IAC1B,MAAM,OAAO,aAAa;IAC1B,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,QAAQ,IAAI;AACrE;AASO,SAAS,MAAM,KAAK,EAAE,KAAK;IAChC,QAAQ,eAAe;IACvB,QAAQ,aAAa;IACrB,IAAI,MAAM,IAAI,KAAK,SAAS,MAAM,IAAI,KAAK,OAAO;QAChD,MAAM,IAAI,IAAI;IAChB;IACA,IAAI,MAAM,IAAI,KAAK,SAAS;QAC1B,MAAM,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO;IAC/B,OAAO;QACL,MAAM,MAAM,CAAC,EAAE,GAAG;IACpB;IACA,OAAO,eAAe;AACxB;AACO,SAAS,kBAAkB,KAAK,EAAE,KAAK,EAAE,OAAO;IACrD,IAAI;QACF,OAAO,MAAM,OAAO;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,WAAW,oDAAyB,cAAc;YACpD,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;AACF;AAQO,SAAS,OAAO,KAAK,EAAE,WAAW;IACvC,QAAQ,eAAe;IACvB,cAAc,aAAa;IAC3B,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAC9B,MAAM,MAAM,CAAC,EAAE,IAAI,IAAI;IACzB,OAAO,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU;QACrE,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,EAAG;YAC7B,MAAM,MAAM,CAAC,EAAE,IAAI,IAAI;QACzB;IACF;IACA,OAAO,eAAe;AACxB;AACO,SAAS,mBAAmB,KAAK,EAAE,WAAW,EAAE,OAAO;IAC5D,IAAI;QACF,OAAO,OAAO,OAAO;IACvB,EAAE,OAAO,OAAO;QACd,IAAI,WAAW,oDAAyB,cAAc;YACpD,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;AACF;AAQO,SAAS,QAAQ,KAAK,EAAE,WAAW;IACxC,QAAQ,eAAe;IACvB,cAAc,aAAa;IAC3B,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAC9B,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,MAAM,MAAM,CAAC,EAAE,IAAI;IAC/C,OAAO,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,EAAG;YAC7B,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,MAAM,MAAM,CAAC,EAAE,IAAI;QAC/C;IACF,OAAO,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,EAAG;YAC7B,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,MAAM,MAAM,CAAC,EAAE,IAAI;QAC7C;IACF;IACA,OAAO,eAAe;AACxB;AACO,SAAS,oBAAoB,KAAK,EAAE,WAAW,EAAE,OAAO;IAC7D,IAAI;QACF,OAAO,QAAQ,OAAO;IACxB,EAAE,OAAO,OAAO;QACd,IAAI,WAAW,oDAAyB,cAAc;YACpD,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;AACF;AASO,SAAS,UAAU,KAAK,EAAE,cAAc,IAAI;IACjD,OAAO,aAAa,SAAS,MAAM,OAAO,OAAO,eAAe,QAAQ,OAAO;AACjF;AACO,SAAS,sBAAsB,KAAK,EAAE,WAAW,EAAE,OAAO;IAC/D,IAAI;QACF,OAAO,UAAU,OAAO;IAC1B,EAAE,OAAO,OAAO;QACd,IAAI,WAAW,oDAAyB,cAAc;YACpD,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;AACF;AAUO,SAAS,MAAM,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,GAAG;IAC7D,MAAM,eAAe,CAAC,GAAG,IAAM,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,OAAO,KAAK;IAC7G,MAAM,kBAAkB,eAAe;IACvC,MAAM,eAAe,eAAe;IACpC,MAAM,MAAM;QAAC,aAAa,gBAAgB,MAAM,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE;QAAG,aAAa,gBAAgB,MAAM,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE;QAAG,aAAa,gBAAgB,MAAM,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE;KAAE;IAC/M,OAAO,eAAe;QACpB,MAAM;QACN,QAAQ;IACV;AACF", "ignoreList": [0]}}, {"offset": {"line": 2345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/cssVars/cssVarsParser.js"], "sourcesContent": ["/**\n * This function create an object from keys, value and then assign to target\n *\n * @param {Object} obj : the target object to be assigned\n * @param {string[]} keys\n * @param {string | number} value\n *\n * @example\n * const source = {}\n * assignNestedKeys(source, ['palette', 'primary'], 'var(--palette-primary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)' } }\n *\n * @example\n * const source = { palette: { primary: 'var(--palette-primary)' } }\n * assignNestedKeys(source, ['palette', 'secondary'], 'var(--palette-secondary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)', secondary: 'var(--palette-secondary)' } }\n */\nexport const assignNestedKeys = (obj, keys, value, arrayKeys = []) => {\n  let temp = obj;\n  keys.forEach((k, index) => {\n    if (index === keys.length - 1) {\n      if (Array.isArray(temp)) {\n        temp[Number(k)] = value;\n      } else if (temp && typeof temp === 'object') {\n        temp[k] = value;\n      }\n    } else if (temp && typeof temp === 'object') {\n      if (!temp[k]) {\n        temp[k] = arrayKeys.includes(k) ? [] : {};\n      }\n      temp = temp[k];\n    }\n  });\n};\n\n/**\n *\n * @param {Object} obj : source object\n * @param {Function} callback : a function that will be called when\n *                   - the deepest key in source object is reached\n *                   - the value of the deepest key is NOT `undefined` | `null`\n *\n * @example\n * walkObjectDeep({ palette: { primary: { main: '#000000' } } }, console.log)\n * // ['palette', 'primary', 'main'] '#000000'\n */\nexport const walkObjectDeep = (obj, callback, shouldSkipPaths) => {\n  function recurse(object, parentKeys = [], arrayKeys = []) {\n    Object.entries(object).forEach(([key, value]) => {\n      if (!shouldSkipPaths || shouldSkipPaths && !shouldSkipPaths([...parentKeys, key])) {\n        if (value !== undefined && value !== null) {\n          if (typeof value === 'object' && Object.keys(value).length > 0) {\n            recurse(value, [...parentKeys, key], Array.isArray(value) ? [...arrayKeys, key] : arrayKeys);\n          } else {\n            callback([...parentKeys, key], value, arrayKeys);\n          }\n        }\n      }\n    });\n  }\n  recurse(obj);\n};\nconst getCssValue = (keys, value) => {\n  if (typeof value === 'number') {\n    if (['lineHeight', 'fontWeight', 'opacity', 'zIndex'].some(prop => keys.includes(prop))) {\n      // CSS property that are unitless\n      return value;\n    }\n    const lastKey = keys[keys.length - 1];\n    if (lastKey.toLowerCase().includes('opacity')) {\n      // opacity values are unitless\n      return value;\n    }\n    return `${value}px`;\n  }\n  return value;\n};\n\n/**\n * a function that parse theme and return { css, vars }\n *\n * @param {Object} theme\n * @param {{\n *  prefix?: string,\n *  shouldSkipGeneratingVar?: (objectPathKeys: Array<string>, value: string | number) => boolean\n * }} options.\n *  `prefix`: The prefix of the generated CSS variables. This function does not change the value.\n *\n * @returns {{ css: Object, vars: Object }} `css` is the stylesheet, `vars` is an object to get css variable (same structure as theme).\n *\n * @example\n * const { css, vars } = parser({\n *   fontSize: 12,\n *   lineHeight: 1.2,\n *   palette: { primary: { 500: 'var(--color)' } }\n * }, { prefix: 'foo' })\n *\n * console.log(css) // { '--foo-fontSize': '12px', '--foo-lineHeight': 1.2, '--foo-palette-primary-500': 'var(--color)' }\n * console.log(vars) // { fontSize: 'var(--foo-fontSize)', lineHeight: 'var(--foo-lineHeight)', palette: { primary: { 500: 'var(--foo-palette-primary-500)' } } }\n */\nexport default function cssVarsParser(theme, options) {\n  const {\n    prefix,\n    shouldSkipGeneratingVar\n  } = options || {};\n  const css = {};\n  const vars = {};\n  const varsWithDefaults = {};\n  walkObjectDeep(theme, (keys, value, arrayKeys) => {\n    if (typeof value === 'string' || typeof value === 'number') {\n      if (!shouldSkipGeneratingVar || !shouldSkipGeneratingVar(keys, value)) {\n        // only create css & var if `shouldSkipGeneratingVar` return false\n        const cssVar = `--${prefix ? `${prefix}-` : ''}${keys.join('-')}`;\n        const resolvedValue = getCssValue(keys, value);\n        Object.assign(css, {\n          [cssVar]: resolvedValue\n        });\n        assignNestedKeys(vars, keys, `var(${cssVar})`, arrayKeys);\n        assignNestedKeys(varsWithDefaults, keys, `var(${cssVar}, ${resolvedValue})`, arrayKeys);\n      }\n    }\n  }, keys => keys[0] === 'vars' // skip 'vars/*' paths\n  );\n  return {\n    css,\n    vars,\n    varsWithDefaults\n  };\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;CAgBC;;;;;AACM,MAAM,mBAAmB,CAAC,KAAK,MAAM,OAAO,YAAY,EAAE;IAC/D,IAAI,OAAO;IACX,KAAK,OAAO,CAAC,CAAC,GAAG;QACf,IAAI,UAAU,KAAK,MAAM,GAAG,GAAG;YAC7B,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,IAAI,CAAC,OAAO,GAAG,GAAG;YACpB,OAAO,IAAI,QAAQ,OAAO,SAAS,UAAU;gBAC3C,IAAI,CAAC,EAAE,GAAG;YACZ;QACF,OAAO,IAAI,QAAQ,OAAO,SAAS,UAAU;YAC3C,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACZ,IAAI,CAAC,EAAE,GAAG,UAAU,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC;YAC1C;YACA,OAAO,IAAI,CAAC,EAAE;QAChB;IACF;AACF;AAaO,MAAM,iBAAiB,CAAC,KAAK,UAAU;IAC5C,SAAS,QAAQ,MAAM,EAAE,aAAa,EAAE,EAAE,YAAY,EAAE;QACtD,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,CAAC,mBAAmB,mBAAmB,CAAC,gBAAgB;mBAAI;gBAAY;aAAI,GAAG;gBACjF,IAAI,UAAU,aAAa,UAAU,MAAM;oBACzC,IAAI,OAAO,UAAU,YAAY,OAAO,IAAI,CAAC,OAAO,MAAM,GAAG,GAAG;wBAC9D,QAAQ,OAAO;+BAAI;4BAAY;yBAAI,EAAE,MAAM,OAAO,CAAC,SAAS;+BAAI;4BAAW;yBAAI,GAAG;oBACpF,OAAO;wBACL,SAAS;+BAAI;4BAAY;yBAAI,EAAE,OAAO;oBACxC;gBACF;YACF;QACF;IACF;IACA,QAAQ;AACV;AACA,MAAM,cAAc,CAAC,MAAM;IACzB,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI;YAAC;YAAc;YAAc;YAAW;SAAS,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,QAAQ;YACvF,iCAAiC;YACjC,OAAO;QACT;QACA,MAAM,UAAU,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACrC,IAAI,QAAQ,WAAW,GAAG,QAAQ,CAAC,YAAY;YAC7C,8BAA8B;YAC9B,OAAO;QACT;QACA,OAAO,GAAG,MAAM,EAAE,CAAC;IACrB;IACA,OAAO;AACT;AAwBe,SAAS,cAAc,KAAK,EAAE,OAAO;IAClD,MAAM,EACJ,MAAM,EACN,uBAAuB,EACxB,GAAG,WAAW,CAAC;IAChB,MAAM,MAAM,CAAC;IACb,MAAM,OAAO,CAAC;IACd,MAAM,mBAAmB,CAAC;IAC1B,eAAe,OAAO,CAAC,MAAM,OAAO;QAClC,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;YAC1D,IAAI,CAAC,2BAA2B,CAAC,wBAAwB,MAAM,QAAQ;gBACrE,kEAAkE;gBAClE,MAAM,SAAS,CAAC,EAAE,EAAE,SAAS,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,MAAM;gBACjE,MAAM,gBAAgB,YAAY,MAAM;gBACxC,OAAO,MAAM,CAAC,KAAK;oBACjB,CAAC,OAAO,EAAE;gBACZ;gBACA,iBAAiB,MAAM,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;gBAC/C,iBAAiB,kBAAkB,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,cAAc,CAAC,CAAC,EAAE;YAC/E;QACF;IACF,GAAG,CAAA,OAAQ,IAAI,CAAC,EAAE,KAAK,OAAO,sBAAsB;;IAEpD,OAAO;QACL;QACA;QACA;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2469, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/cssVars/prepareCssVars.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nimport cssVarsParser from \"./cssVarsParser.js\";\nfunction prepareCssVars(theme, parserConfig = {}) {\n  const {\n    getSelector = defaultGetSelector,\n    disableCssColorScheme,\n    colorSchemeSelector: selector\n  } = parserConfig;\n  // @ts-ignore - ignore components do not exist\n  const {\n    colorSchemes = {},\n    components,\n    defaultColorScheme = 'light',\n    ...otherTheme\n  } = theme;\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n    [defaultColorScheme]: defaultScheme,\n    ...otherColorSchemes\n  } = colorSchemes;\n  Object.entries(otherColorSchemes || {}).forEach(([key, scheme]) => {\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (defaultScheme) {\n    // default color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(defaultScheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[defaultColorScheme] = {\n      css,\n      vars\n    };\n  }\n  function defaultGetSelector(colorScheme, cssObject) {\n    let rule = selector;\n    if (selector === 'class') {\n      rule = '.%s';\n    }\n    if (selector === 'data') {\n      rule = '[data-%s]';\n    }\n    if (selector?.startsWith('data-') && !selector.includes('%s')) {\n      // 'data-joy-color-scheme' -> '[data-joy-color-scheme=\"%s\"]'\n      rule = `[${selector}=\"%s\"]`;\n    }\n    if (colorScheme) {\n      if (rule === 'media') {\n        if (theme.defaultColorScheme === colorScheme) {\n          return ':root';\n        }\n        const mode = colorSchemes[colorScheme]?.palette?.mode || colorScheme;\n        return {\n          [`@media (prefers-color-scheme: ${mode})`]: {\n            ':root': cssObject\n          }\n        };\n      }\n      if (rule) {\n        if (theme.defaultColorScheme === colorScheme) {\n          return `:root, ${rule.replace('%s', String(colorScheme))}`;\n        }\n        return rule.replace('%s', String(colorScheme));\n      }\n    }\n    return ':root';\n  }\n  const generateThemeVars = () => {\n    let vars = {\n      ...rootVars\n    };\n    Object.entries(colorSchemesMap).forEach(([, {\n      vars: schemeVars\n    }]) => {\n      vars = deepmerge(vars, schemeVars);\n    });\n    return vars;\n  };\n  const generateStyleSheets = () => {\n    const stylesheets = [];\n    const colorScheme = theme.defaultColorScheme || 'light';\n    function insertStyleSheet(key, css) {\n      if (Object.keys(css).length) {\n        stylesheets.push(typeof key === 'string' ? {\n          [key]: {\n            ...css\n          }\n        } : key);\n      }\n    }\n    insertStyleSheet(getSelector(undefined, {\n      ...rootCss\n    }), rootCss);\n    const {\n      [colorScheme]: defaultSchemeVal,\n      ...other\n    } = colorSchemesMap;\n    if (defaultSchemeVal) {\n      // default color scheme has to come before other color schemes\n      const {\n        css\n      } = defaultSchemeVal;\n      const cssColorSheme = colorSchemes[colorScheme]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(colorScheme, {\n        ...finalCss\n      }), finalCss);\n    }\n    Object.entries(other).forEach(([key, {\n      css\n    }]) => {\n      const cssColorSheme = colorSchemes[key]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(key, {\n        ...finalCss\n      }), finalCss);\n    });\n    return stylesheets;\n  };\n  return {\n    vars: themeVars,\n    generateThemeVars,\n    generateStyleSheets\n  };\n}\nexport default prepareCssVars;"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,SAAS,eAAe,KAAK,EAAE,eAAe,CAAC,CAAC;IAC9C,MAAM,EACJ,cAAc,kBAAkB,EAChC,qBAAqB,EACrB,qBAAqB,QAAQ,EAC9B,GAAG;IACJ,8CAA8C;IAC9C,MAAM,EACJ,eAAe,CAAC,CAAC,EACjB,UAAU,EACV,qBAAqB,OAAO,EAC5B,GAAG,YACJ,GAAG;IACJ,MAAM,EACJ,MAAM,QAAQ,EACd,KAAK,OAAO,EACZ,kBAAkB,oBAAoB,EACvC,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,YAAY;IAC9B,IAAI,YAAY;IAChB,MAAM,kBAAkB,CAAC;IACzB,MAAM,EACJ,CAAC,mBAAmB,EAAE,aAAa,EACnC,GAAG,mBACJ,GAAG;IACJ,OAAO,OAAO,CAAC,qBAAqB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO;QAC5D,MAAM,EACJ,IAAI,EACJ,GAAG,EACH,gBAAgB,EACjB,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,QAAQ;QAC1B,YAAY,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,WAAW;QACjC,eAAe,CAAC,IAAI,GAAG;YACrB;YACA;QACF;IACF;IACA,IAAI,eAAe;QACjB,oEAAoE;QACpE,MAAM,EACJ,GAAG,EACH,IAAI,EACJ,gBAAgB,EACjB,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,eAAe;QACjC,YAAY,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,WAAW;QACjC,eAAe,CAAC,mBAAmB,GAAG;YACpC;YACA;QACF;IACF;IACA,SAAS,mBAAmB,WAAW,EAAE,SAAS;QAChD,IAAI,OAAO;QACX,IAAI,aAAa,SAAS;YACxB,OAAO;QACT;QACA,IAAI,aAAa,QAAQ;YACvB,OAAO;QACT;QACA,IAAI,UAAU,WAAW,YAAY,CAAC,SAAS,QAAQ,CAAC,OAAO;YAC7D,4DAA4D;YAC5D,OAAO,CAAC,CAAC,EAAE,SAAS,MAAM,CAAC;QAC7B;QACA,IAAI,aAAa;YACf,IAAI,SAAS,SAAS;gBACpB,IAAI,MAAM,kBAAkB,KAAK,aAAa;oBAC5C,OAAO;gBACT;gBACA,MAAM,OAAO,YAAY,CAAC,YAAY,EAAE,SAAS,QAAQ;gBACzD,OAAO;oBACL,CAAC,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;wBAC1C,SAAS;oBACX;gBACF;YACF;YACA,IAAI,MAAM;gBACR,IAAI,MAAM,kBAAkB,KAAK,aAAa;oBAC5C,OAAO,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,MAAM,OAAO,eAAe;gBAC5D;gBACA,OAAO,KAAK,OAAO,CAAC,MAAM,OAAO;YACnC;QACF;QACA,OAAO;IACT;IACA,MAAM,oBAAoB;QACxB,IAAI,OAAO;YACT,GAAG,QAAQ;QACb;QACA,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,GAAG,EAC1C,MAAM,UAAU,EACjB,CAAC;YACA,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,MAAM;QACzB;QACA,OAAO;IACT;IACA,MAAM,sBAAsB;QAC1B,MAAM,cAAc,EAAE;QACtB,MAAM,cAAc,MAAM,kBAAkB,IAAI;QAChD,SAAS,iBAAiB,GAAG,EAAE,GAAG;YAChC,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE;gBAC3B,YAAY,IAAI,CAAC,OAAO,QAAQ,WAAW;oBACzC,CAAC,IAAI,EAAE;wBACL,GAAG,GAAG;oBACR;gBACF,IAAI;YACN;QACF;QACA,iBAAiB,YAAY,WAAW;YACtC,GAAG,OAAO;QACZ,IAAI;QACJ,MAAM,EACJ,CAAC,YAAY,EAAE,gBAAgB,EAC/B,GAAG,OACJ,GAAG;QACJ,IAAI,kBAAkB;YACpB,8DAA8D;YAC9D,MAAM,EACJ,GAAG,EACJ,GAAG;YACJ,MAAM,gBAAgB,YAAY,CAAC,YAAY,EAAE,SAAS;YAC1D,MAAM,WAAW,CAAC,yBAAyB,gBAAgB;gBACzD,aAAa;gBACb,GAAG,GAAG;YACR,IAAI;gBACF,GAAG,GAAG;YACR;YACA,iBAAiB,YAAY,aAAa;gBACxC,GAAG,QAAQ;YACb,IAAI;QACN;QACA,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,EACnC,GAAG,EACJ,CAAC;YACA,MAAM,gBAAgB,YAAY,CAAC,IAAI,EAAE,SAAS;YAClD,MAAM,WAAW,CAAC,yBAAyB,gBAAgB;gBACzD,aAAa;gBACb,GAAG,GAAG;YACR,IAAI;gBACF,GAAG,GAAG;YACR;YACA,iBAAiB,YAAY,KAAK;gBAChC,GAAG,QAAQ;YACb,IAAI;QACN;QACA,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;uCACe", "ignoreList": [0]}}, {"offset": {"line": 2594, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2620, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/cssVars/getColorSchemeSelector.js"], "sourcesContent": ["/* eslint-disable import/prefer-default-export */\nexport function createGetColorSchemeSelector(selector) {\n  return function getColorSchemeSelector(colorScheme) {\n    if (selector === 'media') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (colorScheme !== 'light' && colorScheme !== 'dark') {\n          console.error(`MUI: @media (prefers-color-scheme) supports only 'light' or 'dark', but receive '${colorScheme}'.`);\n        }\n      }\n      return `@media (prefers-color-scheme: ${colorScheme})`;\n    }\n    if (selector) {\n      if (selector.startsWith('data-') && !selector.includes('%s')) {\n        return `[${selector}=\"${colorScheme}\"] &`;\n      }\n      if (selector === 'class') {\n        return `.${colorScheme} &`;\n      }\n      if (selector === 'data') {\n        return `[data-${colorScheme}] &`;\n      }\n      return `${selector.replace('%s', colorScheme)} &`;\n    }\n    return '&';\n  };\n}"], "names": [], "mappings": "AAAA,+CAA+C;;;AACxC,SAAS,6BAA6B,QAAQ;IACnD,OAAO,SAAS,uBAAuB,WAAW;QAChD,IAAI,aAAa,SAAS;YACxB,wCAA2C;gBACzC,IAAI,gBAAgB,WAAW,gBAAgB,QAAQ;oBACrD,QAAQ,KAAK,CAAC,CAAC,iFAAiF,EAAE,YAAY,EAAE,CAAC;gBACnH;YACF;YACA,OAAO,CAAC,8BAA8B,EAAE,YAAY,CAAC,CAAC;QACxD;QACA,IAAI,UAAU;YACZ,IAAI,SAAS,UAAU,CAAC,YAAY,CAAC,SAAS,QAAQ,CAAC,OAAO;gBAC5D,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY,IAAI,CAAC;YAC3C;YACA,IAAI,aAAa,SAAS;gBACxB,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC;YAC5B;YACA,IAAI,aAAa,QAAQ;gBACvB,OAAO,CAAC,MAAM,EAAE,YAAY,GAAG,CAAC;YAClC;YACA,OAAO,GAAG,SAAS,OAAO,CAAC,MAAM,aAAa,EAAE,CAAC;QACnD;QACA,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 2648, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2664, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/cssVars/createGetCssVar.js"], "sourcesContent": ["/**\n * The benefit of this function is to help developers get CSS var from theme without specifying the whole variable\n * and they does not need to remember the prefix (defined once).\n */\nexport default function createGetCssVar(prefix = '') {\n  function appendVar(...vars) {\n    if (!vars.length) {\n      return '';\n    }\n    const value = vars[0];\n    if (typeof value === 'string' && !value.match(/(#|\\(|\\)|(-?(\\d*\\.)?\\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\\d*\\.)?\\d+)$|(\\d+ \\d+ \\d+)/)) {\n      return `, var(--${prefix ? `${prefix}-` : ''}${value}${appendVar(...vars.slice(1))})`;\n    }\n    return `, ${value}`;\n  }\n\n  // AdditionalVars makes `getCssVar` less strict, so it can be use like this `getCssVar('non-mui-variable')` without type error.\n  const getCssVar = (field, ...fallbacks) => {\n    return `var(--${prefix ? `${prefix}-` : ''}${field}${appendVar(...fallbacks)})`;\n  };\n  return getCssVar;\n}"], "names": [], "mappings": "AAAA;;;CAGC;;;AACc,SAAS,gBAAgB,SAAS,EAAE;IACjD,SAAS,UAAU,GAAG,IAAI;QACxB,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,OAAO;QACT;QACA,MAAM,QAAQ,IAAI,CAAC,EAAE;QACrB,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK,CAAC,gHAAgH;YAC5J,OAAO,CAAC,QAAQ,EAAE,SAAS,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,QAAQ,aAAa,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC;QACvF;QACA,OAAO,CAAC,EAAE,EAAE,OAAO;IACrB;IAEA,+HAA+H;IAC/H,MAAM,YAAY,CAAC,OAAO,GAAG;QAC3B,OAAO,CAAC,MAAM,EAAE,SAAS,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,QAAQ,aAAa,WAAW,CAAC,CAAC;IACjF;IACA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 2687, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2703, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/memoTheme.js"], "sourcesContent": ["import preprocessStyles from \"./preprocessStyles.js\";\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\n// We need to pass an argument as `{ theme }` for PigmentCSS, but we don't want to\n// allocate more objects.\nconst arg = {\n  theme: undefined\n};\n\n/**\n * Memoize style function on theme.\n * Intended to be used in styled() calls that only need access to the theme.\n */\nexport default function unstable_memoTheme(styleFn) {\n  let lastValue;\n  let lastTheme;\n  return function styleMemoized(props) {\n    let value = lastValue;\n    if (value === undefined || props.theme !== lastTheme) {\n      arg.theme = props.theme;\n      value = preprocessStyles(styleFn(arg));\n      lastValue = value;\n      lastTheme = props.theme;\n    }\n    return value;\n  };\n}"], "names": [], "mappings": ";;;AAAA;;AAEA,uDAAuD,GAEvD,kFAAkF;AAClF,yBAAyB;AACzB,MAAM,MAAM;IACV,OAAO;AACT;AAMe,SAAS,mBAAmB,OAAO;IAChD,IAAI;IACJ,IAAI;IACJ,OAAO,SAAS,cAAc,KAAK;QACjC,IAAI,QAAQ;QACZ,IAAI,UAAU,aAAa,MAAM,KAAK,KAAK,WAAW;YACpD,IAAI,KAAK,GAAG,MAAM,KAAK;YACvB,QAAQ,CAAA,GAAA,0JAAA,CAAA,UAAgB,AAAD,EAAE,QAAQ;YACjC,YAAY;YACZ,YAAY,MAAM,KAAK;QACzB;QACA,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 2727, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2743, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/DefaultPropsProvider/DefaultPropsProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport resolveProps from '@mui/utils/resolveProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PropsContext = /*#__PURE__*/React.createContext(undefined);\nfunction DefaultPropsProvider({\n  value,\n  children\n}) {\n  return /*#__PURE__*/_jsx(PropsContext.Provider, {\n    value: value,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? DefaultPropsProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  value: PropTypes.object\n} : void 0;\nfunction getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name]) {\n    return props;\n  }\n  const config = theme.components[name];\n  if (config.defaultProps) {\n    // compatible with v5 signature\n    return resolveProps(config.defaultProps, props);\n  }\n  if (!config.styleOverrides && !config.variants) {\n    // v6 signature, no property 'defaultProps'\n    return resolveProps(config, props);\n  }\n  return props;\n}\nexport function useDefaultProps({\n  props,\n  name\n}) {\n  const ctx = React.useContext(PropsContext);\n  return getThemeProps({\n    props,\n    name,\n    theme: {\n      components: ctx\n    }\n  });\n}\nexport default DefaultPropsProvider;"], "names": [], "mappings": ";;;;AAEA;AAGA;AAFA;AACA;AAJA;;;;;AAMA,MAAM,eAAe,WAAW,GAAE,sMAAM,aAAa,CAAC;AACtD,SAAS,qBAAqB,EAC5B,KAAK,EACL,QAAQ,EACT;IACC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,aAAa,QAAQ,EAAE;QAC9C,OAAO;QACP,UAAU;IACZ;AACF;AACA,uCAAwC,qBAAqB,SAAS,GAA0B;IAC9F,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,OAAO,sIAAA,CAAA,UAAS,CAAC,MAAM;AACzB;AACA,SAAS,cAAc,MAAM;IAC3B,MAAM,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACN,GAAG;IACJ,IAAI,CAAC,SAAS,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,CAAC,KAAK,EAAE;QAC1D,OAAO;IACT;IACA,MAAM,SAAS,MAAM,UAAU,CAAC,KAAK;IACrC,IAAI,OAAO,YAAY,EAAE;QACvB,+BAA+B;QAC/B,OAAO,CAAA,GAAA,qKAAA,CAAA,UAAY,AAAD,EAAE,OAAO,YAAY,EAAE;IAC3C;IACA,IAAI,CAAC,OAAO,cAAc,IAAI,CAAC,OAAO,QAAQ,EAAE;QAC9C,2CAA2C;QAC3C,OAAO,CAAA,GAAA,qKAAA,CAAA,UAAY,AAAD,EAAE,QAAQ;IAC9B;IACA,OAAO;AACT;AACO,SAAS,gBAAgB,EAC9B,KAAK,EACL,IAAI,EACL;IACC,MAAM,MAAM,sMAAM,UAAU,CAAC;IAC7B,OAAO,cAAc;QACnB;QACA;QACA,OAAO;YACL,YAAY;QACd;IACF;AACF;uCACe", "ignoreList": [0]}}, {"offset": {"line": 2802, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2808, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/useThemeWithoutDefault/useThemeWithoutDefault.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeContext } from '@mui/styled-engine';\nfunction isObjectEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction useTheme(defaultTheme = null) {\n  const contextTheme = React.useContext(ThemeContext);\n  return !contextTheme || isObjectEmpty(contextTheme) ? defaultTheme : contextTheme;\n}\nexport default useTheme;"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAIA,SAAS,cAAc,GAAG;IACxB,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;AACrC;AACA,SAAS,SAAS,eAAe,IAAI;IACnC,MAAM,eAAe,sMAAM,UAAU,CAAC,uOAAA,CAAA,eAAY;IAClD,OAAO,CAAC,gBAAgB,cAAc,gBAAgB,eAAe;AACvE;uCACe", "ignoreList": [0]}}, {"offset": {"line": 2824, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2830, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/useTheme/useTheme.js"], "sourcesContent": ["'use client';\n\nimport createTheme from \"../createTheme/index.js\";\nimport useThemeWithoutDefault from \"../useThemeWithoutDefault/index.js\";\nexport const systemDefaultTheme = createTheme();\nfunction useTheme(defaultTheme = systemDefaultTheme) {\n  return useThemeWithoutDefault(defaultTheme);\n}\nexport default useTheme;"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAIO,MAAM,qBAAqB,CAAA,GAAA,oKAAA,CAAA,UAAW,AAAD;AAC5C,SAAS,SAAS,eAAe,kBAAkB;IACjD,OAAO,CAAA,GAAA,0LAAA,CAAA,UAAsB,AAAD,EAAE;AAChC;uCACe", "ignoreList": [0]}}, {"offset": {"line": 2844, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2860, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/GlobalStyles/GlobalStyles.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles as MuiGlobalStyles } from '@mui/styled-engine';\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GlobalStyles({\n  styles,\n  themeId,\n  defaultTheme = {}\n}) {\n  const upperTheme = useTheme(defaultTheme);\n  const globalStyles = typeof styles === 'function' ? styles(themeId ? upperTheme[themeId] || upperTheme : upperTheme) : styles;\n  return /*#__PURE__*/_jsx(MuiGlobalStyles, {\n    styles: globalStyles\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  defaultTheme: PropTypes.object,\n  /**\n   * @ignore\n   */\n  styles: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.array, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  themeId: PropTypes.string\n} : void 0;\nexport default GlobalStyles;"], "names": [], "mappings": ";;;AAEA;AAIA;AAHA;AAEA;AADA;AAJA;;;;;;AAOA,SAAS,aAAa,EACpB,MAAM,EACN,OAAO,EACP,eAAe,CAAC,CAAC,EAClB;IACC,MAAM,aAAa,CAAA,GAAA,8JAAA,CAAA,UAAQ,AAAD,EAAE;IAC5B,MAAM,eAAe,OAAO,WAAW,aAAa,OAAO,UAAU,UAAU,CAAC,QAAQ,IAAI,aAAa,cAAc;IACvH,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,oNAAA,CAAA,eAAe,EAAE;QACxC,QAAQ;IACV;AACF;AACA,uCAAwC,aAAa,SAAS,GAA0B;IACtF,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,cAAc,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC9B;;GAEC,GACD,QAAQ,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK;QAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;KAAC;IACzK;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;AAC3B;uCACe", "ignoreList": [0]}}, {"offset": {"line": 2904, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2920, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/styleFunctionSx/extendSxProp.js"], "sourcesContent": ["import { isPlainObject } from '@mui/utils/deepmerge';\nimport defaultSxConfig from \"./defaultSxConfig.js\";\nconst splitProps = props => {\n  const result = {\n    systemProps: {},\n    otherProps: {}\n  };\n  const config = props?.theme?.unstable_sxConfig ?? defaultSxConfig;\n  Object.keys(props).forEach(prop => {\n    if (config[prop]) {\n      result.systemProps[prop] = props[prop];\n    } else {\n      result.otherProps[prop] = props[prop];\n    }\n  });\n  return result;\n};\nexport default function extendSxProp(props) {\n  const {\n    sx: inSx,\n    ...other\n  } = props;\n  const {\n    systemProps,\n    otherProps\n  } = splitProps(other);\n  let finalSx;\n  if (Array.isArray(inSx)) {\n    finalSx = [systemProps, ...inSx];\n  } else if (typeof inSx === 'function') {\n    finalSx = (...args) => {\n      const result = inSx(...args);\n      if (!isPlainObject(result)) {\n        return systemProps;\n      }\n      return {\n        ...systemProps,\n        ...result\n      };\n    };\n  } else {\n    finalSx = {\n      ...systemProps,\n      ...inSx\n    };\n  }\n  return {\n    ...otherProps,\n    sx: finalSx\n  };\n}"], "names": [], "mappings": ";;;AACA;AADA;;;AAEA,MAAM,aAAa,CAAA;IACjB,MAAM,SAAS;QACb,aAAa,CAAC;QACd,YAAY,CAAC;IACf;IACA,MAAM,SAAS,OAAO,OAAO,qBAAqB,4KAAA,CAAA,UAAe;IACjE,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACzB,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,OAAO,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;QACxC,OAAO;YACL,OAAO,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;QACvC;IACF;IACA,OAAO;AACT;AACe,SAAS,aAAa,KAAK;IACxC,MAAM,EACJ,IAAI,IAAI,EACR,GAAG,OACJ,GAAG;IACJ,MAAM,EACJ,WAAW,EACX,UAAU,EACX,GAAG,WAAW;IACf,IAAI;IACJ,IAAI,MAAM,OAAO,CAAC,OAAO;QACvB,UAAU;YAAC;eAAgB;SAAK;IAClC,OAAO,IAAI,OAAO,SAAS,YAAY;QACrC,UAAU,CAAC,GAAG;YACZ,MAAM,SAAS,QAAQ;YACvB,IAAI,CAAC,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;gBAC1B,OAAO;YACT;YACA,OAAO;gBACL,GAAG,WAAW;gBACd,GAAG,MAAM;YACX;QACF;IACF,OAAO;QACL,UAAU;YACR,GAAG,WAAW;YACd,GAAG,IAAI;QACT;IACF;IACA,OAAO;QACL,GAAG,UAAU;QACb,IAAI;IACN;AACF", "ignoreList": [0]}}, {"offset": {"line": 2973, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2989, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/node_modules/%40mui/system/esm/RtlProvider/index.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RtlContext = /*#__PURE__*/React.createContext();\nfunction RtlProvider({\n  value,\n  ...props\n}) {\n  return /*#__PURE__*/_jsx(RtlContext.Provider, {\n    value: value ?? true,\n    ...props\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RtlProvider.propTypes = {\n  children: PropTypes.node,\n  value: PropTypes.bool\n} : void 0;\nexport const useRtl = () => {\n  const value = React.useContext(RtlContext);\n  return value ?? false;\n};\nexport default RtlProvider;"], "names": [], "mappings": ";;;;AAEA;AAEA;AADA;AAHA;;;;AAKA,MAAM,aAAa,WAAW,GAAE,sMAAM,aAAa;AACnD,SAAS,YAAY,EACnB,KAAK,EACL,GAAG,OACJ;IACC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,WAAW,QAAQ,EAAE;QAC5C,OAAO,SAAS;QAChB,GAAG,KAAK;IACV;AACF;AACA,uCAAwC,YAAY,SAAS,GAAG;IAC9D,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB,OAAO,sIAAA,CAAA,UAAS,CAAC,IAAI;AACvB;AACO,MAAM,SAAS;IACpB,MAAM,QAAQ,sMAAM,UAAU,CAAC;IAC/B,OAAO,SAAS;AAClB;uCACe", "ignoreList": [0]}}, {"offset": {"line": 3016, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}